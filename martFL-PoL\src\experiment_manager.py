#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验管理器 - 系统性实验配置和管理
支持多维度参数组合，自动生成实验计划
"""

import itertools
import json
import os
import time
import hashlib
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, asdict
import logging
from pathlib import Path

@dataclass
class ExperimentConfig:
    """单个实验配置"""
    # 基础参数
    model_name: str = 'LeNet'
    dataset: str = 'MNIST'
    aggregator: str = 'FedAvg'
    n_participant: int = 10
    n_adversary: int = 0
    attack: str = 'None'
    
    # 训练参数
    global_epoch: int = 20
    local_epoch: int = 2
    learning_rate: float = 0.001
    batch_size: int = 32
    
    # PoL参数
    enable_pol: bool = False
    pol_verification_threshold: float = 10.0
    pol_verification_budget: int = 1
    pol_save_strategy: str = 'epoch'
    pol_entropy_analysis: bool = False
    pol_quality_aware: bool = False
    
    # 实验控制
    device: str = 'auto'
    seed: int = 42
    run_id: int = 1
    
    # 元数据
    experiment_id: str = ''
    timestamp: float = 0.0
    
    def __post_init__(self):
        """后处理：生成实验ID和时间戳"""
        if not self.experiment_id:
            self.experiment_id = self._generate_experiment_id()
        if self.timestamp == 0.0:
            self.timestamp = time.time()
    
    def _generate_experiment_id(self) -> str:
        """生成唯一的实验ID"""
        # 使用关键参数生成哈希
        key_params = f"{self.model_name}_{self.dataset}_{self.aggregator}_{self.attack}_{self.n_participant}_{self.n_adversary}_{self.enable_pol}_{self.seed}_{self.run_id}"
        hash_obj = hashlib.md5(key_params.encode())
        return hash_obj.hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def to_args_list(self) -> List[str]:
        """转换为命令行参数列表"""
        args = []
        
        # 基础参数
        args.extend(['--model_name', self.model_name])
        args.extend(['--dataset', self.dataset])
        args.extend(['--aggregator', self.aggregator])
        args.extend(['--n_participant', str(self.n_participant)])
        args.extend(['--n_adversary', str(self.n_adversary)])
        args.extend(['--attack', self.attack])
        
        # 训练参数
        args.extend(['--global_epoch', str(self.global_epoch)])
        args.extend(['--local_epoch', str(self.local_epoch)])
        args.extend(['--learning_rate', str(self.learning_rate)])
        args.extend(['--batch_size', str(self.batch_size)])
        
        # PoL参数
        if self.enable_pol:
            args.append('--enable_pol')
            args.extend(['--pol_verification_threshold', str(self.pol_verification_threshold)])
            args.extend(['--pol_verification_budget', str(self.pol_verification_budget)])
            args.extend(['--pol_save_strategy', self.pol_save_strategy])
            if self.pol_entropy_analysis:
                args.append('--pol_entropy_analysis')
            if self.pol_quality_aware:
                args.append('--pol_quality_aware')
        
        # 设备和种子
        if self.device != 'auto':
            args.extend(['--device', self.device])
        
        return args

class ExperimentManager:
    """实验管理器"""
    
    def __init__(self, base_output_dir: str = "experiments"):
        """
        初始化实验管理器
        
        Args:
            base_output_dir: 实验输出基础目录
        """
        self.base_output_dir = Path(base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        
        self.logger = self._setup_logger()
        
        # 实验配置
        self.experiment_configs: List[ExperimentConfig] = []
        self.experiment_plan: Dict[str, Any] = {}
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ExperimentManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def create_baseline_comparison_experiments(self) -> List[ExperimentConfig]:
        """创建基线对比实验 - 简化版：只对比martFL vs martFL-PoL"""
        experiments = []

        # 简化的基础配置 - 专注于martFL vs martFL-PoL对比
        base_configs = {
            'datasets': ['MNIST', 'CIFAR', 'TREC', 'AGNEWS'],  # 保留核心数据集
            'n_participants': [10, 20, 50],  # 简化参与者规模
            'global_epochs': [20, 30],  # 简化轮次选择
            'runs': list(range(1, 6))  # 5次重复实验提高统计显著性
        }

        # 生成martFL vs martFL-PoL对比实验
        for dataset, n_part, epochs, run in itertools.product(
            base_configs['datasets'],
            base_configs['n_participants'],
            base_configs['global_epochs'],
            base_configs['runs']
        ):
            # 基线组：原版martFL（无PoL）
            baseline_config = ExperimentConfig(
                dataset=dataset,
                aggregator='martFL',
                n_participant=n_part,
                global_epoch=epochs,
                enable_pol=False,
                run_id=run,
                seed=42 + run,
                experiment_id=f"baseline_martFL_{dataset}_{n_part}p_{epochs}e_run{run}"
            )
            experiments.append(baseline_config)

            # 实验组：martFL-PoL
            pol_config = ExperimentConfig(
                dataset=dataset,
                aggregator='martFL',
                n_participant=n_part,
                global_epoch=epochs,
                enable_pol=True,
                pol_entropy_analysis=True,
                pol_quality_aware=True,
                run_id=run,
                seed=42 + run,
                experiment_id=f"pol_martFL_{dataset}_{n_part}p_{epochs}e_run{run}"
            )
            experiments.append(pol_config)

        self.logger.info(f"生成了 {len(experiments)} 个基线对比实验 (martFL vs martFL-PoL)")
        return experiments
    
    def create_attack_robustness_experiments(self) -> List[ExperimentConfig]:
        """创建攻击鲁棒性实验"""
        experiments = []

        # 优化的攻击配置 - 基于martFL-PoL防御能力分析优化
        attack_configs = {
            'datasets': ['MNIST', 'CIFAR', 'TREC'],  # 去掉ImageNet100减少计算量
            'attacks': ['free_rider', 'rescaling', 'sybil', 'label_flipping', 'backdoor'],  # 5种核心攻击
            'aggregators': ['FedAvg', 'martFL', 'Krum', 'FLTrust', 'FLAME', 'BayBFed', 'FedRoLA', 'SDEAHFL'],  # 8种优化防御方法
            'n_participants': [10, 20, 50],
            'adversary_ratios': [0.1, 0.2, 0.3],  # 减少到3种恶意比例
            'runs': list(range(1, 4))  # 3次重复实验
        }
        
        for dataset, attack, aggregator, n_part, adv_ratio, run in itertools.product(
            attack_configs['datasets'],
            attack_configs['attacks'],
            attack_configs['aggregators'],
            attack_configs['n_participants'],
            attack_configs['adversary_ratios'],
            attack_configs['runs']
        ):
            n_adversary = max(1, int(n_part * adv_ratio))
            
            # 基线攻击实验
            config = ExperimentConfig(
                dataset=dataset,
                aggregator=aggregator,
                attack=attack,
                n_participant=n_part,
                n_adversary=n_adversary,
                global_epoch=25,
                enable_pol=False,
                run_id=run,
                seed=42 + run
            )
            experiments.append(config)
            
            # PoL防御实验（仅对martFL）
            if aggregator == 'martFL':
                pol_config = ExperimentConfig(
                    dataset=dataset,
                    aggregator=aggregator,
                    attack=attack,
                    n_participant=n_part,
                    n_adversary=n_adversary,
                    global_epoch=25,
                    enable_pol=True,
                    pol_entropy_analysis=True,
                    pol_quality_aware=True,
                    run_id=run,
                    seed=42 + run
                )
                experiments.append(pol_config)
        
        self.logger.info(f"生成了 {len(experiments)} 个攻击鲁棒性实验")
        return experiments
    
    def create_scalability_experiments(self) -> List[ExperimentConfig]:
        """创建扩展性实验"""
        experiments = []

        # 扩展的扩展性配置 - 使用真实数据集
        scale_configs = {
            'datasets': ['MNIST', 'CIFAR', 'TREC'],  # 仅使用真实数据集
            'n_participants': [10, 20, 50, 100, 200],  # 扩展到200参与者
            'aggregators': ['FedAvg', 'martFL', 'Krum', 'FLAME', 'FedNova'],
            'runs': list(range(1, 4))  # 3次重复
        }
        
        for dataset, n_part, aggregator, run in itertools.product(
            scale_configs['datasets'],
            scale_configs['n_participants'],
            scale_configs['aggregators'],
            scale_configs['runs']
        ):
            # 基线扩展性实验
            config = ExperimentConfig(
                dataset=dataset,
                aggregator=aggregator,
                n_participant=n_part,
                global_epoch=15,  # 减少轮数以节省时间
                enable_pol=False,
                run_id=run,
                seed=42 + run
            )
            experiments.append(config)
            
            # PoL扩展性实验（仅对martFL）
            if aggregator == 'martFL':
                pol_config = ExperimentConfig(
                    dataset=dataset,
                    aggregator=aggregator,
                    n_participant=n_part,
                    global_epoch=15,
                    enable_pol=True,
                    run_id=run,
                    seed=42 + run
                )
                experiments.append(pol_config)
        
        self.logger.info(f"生成了 {len(experiments)} 个扩展性实验")
        return experiments
    
    def create_comprehensive_experiment_plan(self) -> Dict[str, List[ExperimentConfig]]:
        """创建综合实验计划"""
        plan = {
            'baseline_comparison': self.create_baseline_comparison_experiments(),
            'attack_robustness': self.create_attack_robustness_experiments(),
            'scalability': self.create_scalability_experiments()
        }
        
        # 统计信息
        total_experiments = sum(len(experiments) for experiments in plan.values())
        self.logger.info(f"综合实验计划包含 {total_experiments} 个实验")
        
        for category, experiments in plan.items():
            self.logger.info(f"  {category}: {len(experiments)} 个实验")
        
        self.experiment_plan = plan
        return plan
    
    def save_experiment_plan(self, filepath: str):
        """保存实验计划到文件"""
        plan_data = {}
        
        for category, experiments in self.experiment_plan.items():
            plan_data[category] = [exp.to_dict() for exp in experiments]
        
        # 添加元数据
        plan_data['metadata'] = {
            'total_experiments': sum(len(experiments) for experiments in self.experiment_plan.values()),
            'created_at': time.time(),
            'categories': list(self.experiment_plan.keys())
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(plan_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"实验计划已保存到: {filepath}")
    
    def load_experiment_plan(self, filepath: str) -> Dict[str, List[ExperimentConfig]]:
        """从文件加载实验计划"""
        with open(filepath, 'r', encoding='utf-8') as f:
            plan_data = json.load(f)
        
        plan = {}
        for category, experiments_data in plan_data.items():
            if category == 'metadata':
                continue
            
            experiments = []
            for exp_data in experiments_data:
                config = ExperimentConfig(**exp_data)
                experiments.append(config)
            
            plan[category] = experiments
        
        self.experiment_plan = plan
        self.logger.info(f"从 {filepath} 加载了实验计划")
        return plan
    
    def get_experiment_by_id(self, experiment_id: str) -> Optional[ExperimentConfig]:
        """根据ID获取实验配置"""
        for experiments in self.experiment_plan.values():
            for exp in experiments:
                if exp.experiment_id == experiment_id:
                    return exp
        return None
    
    def filter_experiments(self, **filters) -> List[ExperimentConfig]:
        """根据条件过滤实验"""
        filtered = []
        
        for experiments in self.experiment_plan.values():
            for exp in experiments:
                match = True
                for key, value in filters.items():
                    if hasattr(exp, key) and getattr(exp, key) != value:
                        match = False
                        break
                
                if match:
                    filtered.append(exp)
        
        return filtered

if __name__ == "__main__":
    # 测试实验管理器
    em = ExperimentManager()
    
    # 创建综合实验计划
    plan = em.create_comprehensive_experiment_plan()
    
    # 保存实验计划
    em.save_experiment_plan("experiment_plan.json")
    
    # 测试过滤功能
    pol_experiments = em.filter_experiments(enable_pol=True, dataset='MNIST')
    print(f"\n找到 {len(pol_experiments)} 个MNIST+PoL实验")
