#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验控制器 - 统一的实验执行入口
整合设备管理、实验配置、并行执行和结果分析
"""

import argparse
import sys
import time
from pathlib import Path
from typing import List, Optional, Dict, Any
import logging
import json

# 导入自定义模块
from device_manager import get_device_manager
from experiment_manager import ExperimentManager, ExperimentConfig
from parallel_executor import ParallelExecutor
from experiment_analyzer import ExperimentAnalyzer
from incremental_experiment_manager import IncrementalExperimentManager

class ExperimentController:
    """实验控制器 - 统一管理整个实验流程"""
    
    def __init__(self, 
                 output_dir: str = "experiment_results",
                 max_workers: Optional[int] = None,
                 verbose: bool = True):
        """
        初始化实验控制器
        
        Args:
            output_dir: 实验输出目录
            max_workers: 最大并行工作进程数
            verbose: 是否输出详细信息
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.verbose = verbose
        
        # 初始化各个组件
        self.device_manager = get_device_manager(verbose=verbose)
        self.experiment_manager = ExperimentManager(str(self.output_dir))
        self.parallel_executor = ParallelExecutor(
            max_workers=max_workers,
            output_dir=str(self.output_dir),
            verbose=verbose
        )
        self.analyzer = ExperimentAnalyzer(str(self.output_dir))
        self.incremental_manager = IncrementalExperimentManager(str(self.output_dir))

        self.logger = self._setup_logger()

        # 实验筛选参数
        self.experiment_filters = {
            'datasets': None,
            'attacks': None,
            'defenses': None,
            'participants': None,
            'adversary_ratios': None,
            'runs': None,
            'enable_pol': 'both'
        }

        # 增量式实验参数
        self.incremental_mode = False
        self.target_runs = None
        self.force_restart = False

        # 保存系统信息
        self._save_system_info()

    def set_experiment_filters(self, args):
        """设置实验筛选参数"""
        self.experiment_filters.update({
            'datasets': args.datasets,
            'attacks': args.attacks,
            'defenses': args.defenses,
            'participants': args.participants,
            'adversary_ratios': args.adversary_ratios,
            'runs': args.runs,
            'enable_pol': args.enable_pol
        })

        # 设置增量式实验参数
        self.incremental_mode = args.incremental
        self.target_runs = args.target_runs or args.runs or 3
        self.force_restart = args.force_restart

        if self.verbose:
            self.logger.info("🔧 实验配置参数:")
            for key, value in self.experiment_filters.items():
                if value is not None:
                    self.logger.info(f"  {key}: {value}")

            if self.incremental_mode:
                self.logger.info(f"🔄 增量式实验模式: 目标重复次数 {self.target_runs}")
                if self.force_restart:
                    self.logger.info("🗑️ 强制重新开始实验")

    def _filter_experiments(self, experiments: List[ExperimentConfig]) -> List[ExperimentConfig]:
        """根据筛选参数过滤实验"""
        filtered = experiments

        # 数据集筛选
        if self.experiment_filters['datasets']:
            filtered = [exp for exp in filtered if exp.dataset in self.experiment_filters['datasets']]

        # 攻击类型筛选
        if self.experiment_filters['attacks']:
            filtered = [exp for exp in filtered if exp.attack in self.experiment_filters['attacks']]

        # 防御方法筛选
        if self.experiment_filters['defenses']:
            filtered = [exp for exp in filtered if exp.aggregator in self.experiment_filters['defenses']]

        # 参与者数量筛选
        if self.experiment_filters['participants']:
            filtered = [exp for exp in filtered if exp.n_participant in self.experiment_filters['participants']]

        # 恶意参与者比例筛选
        if self.experiment_filters['adversary_ratios']:
            filtered = [exp for exp in filtered
                       if (exp.n_adversary / exp.n_participant) in self.experiment_filters['adversary_ratios']]

        # 重复次数筛选
        if self.experiment_filters['runs']:
            filtered = [exp for exp in filtered if exp.run_id <= self.experiment_filters['runs']]

        # PoL启用筛选
        if self.experiment_filters['enable_pol'] != 'both':
            enable_pol = self.experiment_filters['enable_pol'] == 'true'
            filtered = [exp for exp in filtered if exp.enable_pol == enable_pol]

        if self.verbose and len(filtered) != len(experiments):
            self.logger.info(f"📊 实验筛选结果: {len(experiments)} -> {len(filtered)} 个实验")

        return filtered

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ExperimentController')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件输出
            log_file = self.output_dir / "experiment_controller.log"
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(console_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _save_system_info(self):
        """保存系统信息"""
        system_info_file = self.output_dir / "system_info.json"
        self.device_manager.save_device_info(str(system_info_file))
    
    def run_baseline_experiments(self) -> List[Dict[str, Any]]:
        """运行基线对比实验"""
        self.logger.info("🚀 开始基线对比实验")

        # 生成实验配置
        experiments = self.experiment_manager.create_baseline_comparison_experiments()

        # 应用筛选
        experiments = self._filter_experiments(experiments)

        if not experiments:
            self.logger.warning("⚠️ 筛选后没有实验需要运行")
            return []

        # 处理增量式实验
        if self.incremental_mode:
            return self._run_incremental_experiments(experiments, 'baseline')
        else:
            return self._run_standard_experiments(experiments, 'baseline')
    
    def run_attack_experiments(self) -> List[Dict[str, Any]]:
        """运行攻击鲁棒性实验"""
        self.logger.info("🛡️ 开始攻击鲁棒性实验")

        # 生成实验配置
        experiments = self.experiment_manager.create_attack_robustness_experiments()

        # 应用筛选
        experiments = self._filter_experiments(experiments)

        if not experiments:
            self.logger.warning("⚠️ 筛选后没有实验需要运行")
            return []

        # 处理增量式实验
        if self.incremental_mode:
            return self._run_incremental_experiments(experiments, 'attack')
        else:
            return self._run_standard_experiments(experiments, 'attack')

    def _run_incremental_experiments(self, experiments: List[ExperimentConfig], experiment_type: str) -> List[Dict[str, Any]]:
        """运行增量式实验"""
        # 准备增量式实验
        experiments_to_run, status_info = self.incremental_manager.prepare_incremental_experiments(
            experiments, self.target_runs, self.force_restart
        )

        if status_info['status'] == 'completed':
            self.logger.info(f"✅ 实验已完成 {status_info['completed_runs']}/{status_info['target_runs']} 轮")
            return []

        if not experiments_to_run:
            self.logger.warning("⚠️ 没有需要运行的实验")
            return []

        # 显示状态信息
        self._display_incremental_status(status_info)

        # 保存实验计划
        plan_file = self.output_dir / f"{experiment_type}_experiment_plan_run_{status_info['current_run']}.json"
        plan = {f'{experiment_type}_robustness': experiments_to_run}
        self.experiment_manager.experiment_plan = plan
        self.experiment_manager.save_experiment_plan(str(plan_file))

        # 执行实验
        results = self.parallel_executor.execute_experiments(
            experiments_to_run,
            progress_callback=self._progress_callback
        )

        # 标记当前轮次完成
        self.incremental_manager.mark_run_completed(status_info['current_run'])

        # 显示完成状态
        final_status = self.incremental_manager.get_experiment_status()
        self._display_incremental_completion(final_status)

        return results

    def _run_standard_experiments(self, experiments: List[ExperimentConfig], experiment_type: str) -> List[Dict[str, Any]]:
        """运行标准实验"""
        # 保存实验计划
        plan_file = self.output_dir / f"{experiment_type}_experiment_plan.json"
        plan = {f'{experiment_type}_robustness': experiments}
        self.experiment_manager.experiment_plan = plan
        self.experiment_manager.save_experiment_plan(str(plan_file))

        # 执行实验
        results = self.parallel_executor.execute_experiments(
            experiments,
            progress_callback=self._progress_callback
        )

        self.logger.info(f"✅ {experiment_type}实验完成")
        return results

    def _run_comprehensive_standard_experiments(self, plan: Dict) -> List[Dict[str, Any]]:
        """运行标准综合实验"""
        # 保存实验计划
        plan_file = self.output_dir / "comprehensive_experiment_plan.json"
        self.experiment_manager.save_experiment_plan(str(plan_file))

        # 按类别执行实验
        all_results = []

        for category, experiments in plan.items():
            # 应用筛选
            filtered_experiments = self._filter_experiments(experiments)

            if not filtered_experiments:
                self.logger.info(f"⚠️ {category} 筛选后没有实验需要运行")
                continue

            self.logger.info(f"执行 {category} 实验 ({len(filtered_experiments)} 个)")

            results = self.parallel_executor.execute_experiments(
                filtered_experiments,
                progress_callback=self._progress_callback
            )
            all_results.extend(results)

            # 中间分析
            self._generate_intermediate_analysis(category)

        self.logger.info("✅ 综合实验计划完成")
        return all_results

    def _display_incremental_status(self, status_info: Dict):
        """显示增量式实验状态"""
        print("\n" + "="*80)
        print("🔄 增量式实验状态")
        print("="*80)

        if status_info['is_new_experiment']:
            print("🆕 新实验开始")
        else:
            print("🔄 继续已有实验")

        print(f"📊 当前轮次: {status_info['current_run']}/{status_info['target_runs']}")
        print(f"📈 总体进度: {status_info['completed_runs']}/{status_info['target_runs']} 轮已完成")
        print(f"🧪 本轮实验: {status_info['experiments_this_run']} 个")
        print(f"📋 总实验数: {status_info['total_experiments']} 个")

        progress_percent = (status_info['completed_runs'] / status_info['target_runs']) * 100
        progress_bar = "█" * int(progress_percent // 5) + "░" * (20 - int(progress_percent // 5))
        print(f"⏳ 整体进度: [{progress_bar}] {progress_percent:.1f}%")
        print("="*80)

    def _display_incremental_completion(self, final_status: Dict):
        """显示增量式实验完成状态"""
        print("\n" + "="*80)
        print("✅ 增量式实验轮次完成")
        print("="*80)

        if final_status['status'] == 'completed':
            print("🎉 所有实验轮次已完成！")
            print(f"📊 完成统计: {final_status['completed_runs']}/{final_status['target_runs']} 轮")
            print(f"🧪 总实验数: {final_status['completed_experiments']} 个")
        else:
            print(f"📈 当前进度: {final_status['completed_runs']}/{final_status['target_runs']} 轮")
            print(f"🔄 剩余轮次: {final_status['target_runs'] - final_status['completed_runs']} 轮")
            print(f"🧪 剩余实验: {final_status['remaining_experiments']} 个")
            print("\n💡 提示: 再次运行相同命令继续下一轮实验")

        print("="*80)

    def show_experiment_progress(self):
        """显示当前实验进度"""
        status = self.incremental_manager.get_experiment_status()

        print("\n" + "="*80)
        print("📊 实验进度查看")
        print("="*80)

        if status['status'] == 'no_experiments':
            print("ℹ️ 当前没有进行中的实验")
        else:
            print(f"🔍 实验状态: {status['status']}")
            print(f"📅 创建时间: {status['created_time']}")
            print(f"🕐 最后更新: {status['last_update']}")
            print(f"📊 完成轮次: {status['completed_runs']}/{status['target_runs']}")
            print(f"📈 完成进度: {status['progress_percentage']:.1f}%")
            print(f"🧪 基础配置: {status['total_base_configs']} 个")
            print(f"📋 总实验数: {status['total_experiments']} 个")
            print(f"✅ 已完成: {status['completed_experiments']} 个")
            print(f"⏳ 剩余: {status['remaining_experiments']} 个")

            if status['status'] == 'completed':
                print("\n🎉 所有实验已完成！")
            else:
                print(f"\n💡 提示: 运行实验命令继续下一轮 (第{status['completed_runs']+1}轮)")

        print("="*80)
    
    def run_scalability_experiments(self) -> List[Dict[str, Any]]:
        """运行扩展性实验"""
        self.logger.info("📈 开始扩展性实验")

        # 生成实验配置
        experiments = self.experiment_manager.create_scalability_experiments()

        # 应用筛选
        experiments = self._filter_experiments(experiments)

        if not experiments:
            self.logger.warning("⚠️ 筛选后没有实验需要运行")
            return []

        # 处理增量式实验
        if self.incremental_mode:
            return self._run_incremental_experiments(experiments, 'scalability')
        else:
            return self._run_standard_experiments(experiments, 'scalability')
    
    def run_comprehensive_experiments(self) -> List[Dict[str, Any]]:
        """运行综合实验"""
        self.logger.info("🎯 开始综合实验计划")

        # 生成综合实验计划
        plan = self.experiment_manager.create_comprehensive_experiment_plan()

        # 合并所有实验并应用筛选
        all_experiments = []
        for category, experiments in plan.items():
            all_experiments.extend(experiments)

        all_experiments = self._filter_experiments(all_experiments)

        if not all_experiments:
            self.logger.warning("⚠️ 筛选后没有实验需要运行")
            return []

        # 处理增量式实验
        if self.incremental_mode:
            return self._run_incremental_experiments(all_experiments, 'comprehensive')
        else:
            return self._run_comprehensive_standard_experiments(plan)
    
    def run_custom_experiments(self, experiment_configs: List[ExperimentConfig]) -> List[Dict[str, Any]]:
        """运行自定义实验"""
        self.logger.info(f"🔧 开始自定义实验 ({len(experiment_configs)} 个)")
        
        # 执行实验
        results = self.parallel_executor.execute_experiments(
            experiment_configs,
            progress_callback=self._progress_callback
        )
        
        self.logger.info("✅ 自定义实验完成")
        return results
    
    def _progress_callback(self, completed: int, total: int, result: Any):
        """进度回调函数"""
        if self.verbose:
            progress = completed / total * 100
            status = "✅" if result.success else "❌"
            self.logger.info(f"{status} 进度: {completed}/{total} ({progress:.1f}%) - {result.experiment_id}")
    
    def _generate_intermediate_analysis(self, category: str):
        """生成中间分析结果"""
        try:
            # 重新加载分析器以获取最新数据
            analyzer = ExperimentAnalyzer(str(self.output_dir))
            
            # 生成分类报告
            category_dir = self.output_dir / f"analysis_{category}"
            category_dir.mkdir(exist_ok=True)
            
            analyzer.generate_visualizations(str(category_dir))
            
            report_file = category_dir / f"{category}_report.json"
            analyzer.generate_comprehensive_report(str(report_file))
            
            self.logger.info(f"中间分析结果已保存到: {category_dir}")
            
        except Exception as e:
            self.logger.warning(f"生成中间分析失败: {e}")
    
    def generate_final_analysis(self):
        """生成最终分析报告"""
        self.logger.info("📊 生成最终分析报告")
        
        try:
            # 重新加载分析器
            analyzer = ExperimentAnalyzer(str(self.output_dir))
            
            # 生成可视化
            viz_dir = self.output_dir / "final_analysis"
            analyzer.generate_visualizations(str(viz_dir))
            
            # 生成综合报告
            report_file = self.output_dir / "final_comprehensive_report.json"
            report = analyzer.generate_comprehensive_report(str(report_file))
            
            # 打印关键结果
            self._print_key_results(report)
            
            self.logger.info(f"最终分析报告已保存到: {report_file}")
            self.logger.info(f"可视化图表已保存到: {viz_dir}")
            
        except Exception as e:
            self.logger.error(f"生成最终分析失败: {e}")
    
    def _print_key_results(self, report: Dict[str, Any]):
        """打印关键结果摘要"""
        print("\n" + "="*80)
        print("🎯 实验结果摘要")
        print("="*80)
        
        basic_stats = report.get('basic_statistics', {})
        print(f"📊 总实验数: {basic_stats.get('total_experiments', 0)}")
        print(f"✅ 成功实验: {basic_stats.get('successful_experiments', 0)}")
        print(f"🔬 PoL实验: {basic_stats.get('pol_experiments', 0)}")
        print(f"⚔️ 攻击实验: {basic_stats.get('attack_experiments', 0)}")
        print(f"📚 数据集数: {basic_stats.get('unique_datasets', 0)}")
        print(f"🔄 聚合器数: {basic_stats.get('unique_aggregators', 0)}")
        
        recommendations = report.get('recommendations', [])
        if recommendations:
            print(f"\n💡 改进建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
        print("="*80 + "\n")

def apply_preset_config(args):
    """应用预设配置"""
    if args.preset == 'debug':
        # 调试模式：最小配置，快速测试
        args.datasets = args.datasets or ['MNIST']
        args.attacks = args.attacks or ['free_rider']
        args.defenses = args.defenses or ['martFL']
        args.participants = args.participants or [10]
        args.adversary_ratios = args.adversary_ratios or [0.1]
        args.runs = args.runs or 1
    elif args.preset == 'quick':
        # 快速模式：小规模测试
        args.datasets = args.datasets or ['MNIST', 'CIFAR']
        args.attacks = args.attacks or ['free_rider', 'backdoor']
        args.defenses = args.defenses or ['martFL', 'FLTrust']
        args.participants = args.participants or [10, 20]
        args.adversary_ratios = args.adversary_ratios or [0.1, 0.2]
        args.runs = args.runs or 2
    elif args.preset == 'standard':
        # 标准模式：中等规模实验
        args.datasets = args.datasets or ['MNIST', 'CIFAR', 'TREC']
        args.attacks = args.attacks or ['free_rider', 'rescaling', 'sybil', 'label_flipping']
        args.defenses = args.defenses or ['martFL', 'FLTrust', 'FLAME', 'FedRoLA']
        args.participants = args.participants or [10, 20, 50]
        args.adversary_ratios = args.adversary_ratios or [0.1, 0.2, 0.3]
        args.runs = args.runs or 3
    elif args.preset == 'full':
        # 完整模式：优化后的全面配置
        args.datasets = args.datasets or ['MNIST', 'CIFAR', 'TREC']
        args.attacks = args.attacks or ['free_rider', 'rescaling', 'sybil', 'label_flipping', 'backdoor']
        args.defenses = args.defenses or ['FedAvg', 'martFL', 'Krum', 'FLTrust', 'FLAME', 'BayBFed', 'FedRoLA', 'SDEAHFL']
        args.participants = args.participants or [10, 20, 50]
        args.adversary_ratios = args.adversary_ratios or [0.1, 0.2, 0.3]
        args.runs = args.runs or 3

    return args

def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='martFL-PoL 实验控制器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
实验类型说明:
  baseline    - 基线对比实验 (FedAvg vs martFL vs martFL+PoL)
  attack      - 攻击鲁棒性实验 (各种攻击场景)
  scalability - 扩展性实验 (不同参与者数量)
  comprehensive - 综合实验 (包含以上所有类型)
  custom      - 自定义实验 (从配置文件加载)

预设配置说明:
  debug       - 调试模式 (最小配置，1个数据集，1种攻击，1次重复)
  quick       - 快速模式 (2个数据集，2种攻击，2次重复)
  standard    - 标准模式 (3个数据集，4种攻击，3次重复)
  full        - 完整模式 (所有配置，默认行为)

增量式实验说明:
  所有实验类型都支持增量式重复实验，可以分多次运行：
  --incremental --target_runs 5  # 启用增量式，目标5轮重复
  --show_progress                 # 查看当前进度
  --force_restart                 # 强制重新开始

示例用法:
  # 标准实验
  python experiment_controller.py attack --preset quick
  python experiment_controller.py baseline --preset standard
  python experiment_controller.py scalability --preset quick

  # 增量式实验
  python experiment_controller.py attack --incremental --target_runs 5 --preset quick
  python experiment_controller.py baseline --incremental --target_runs 3 --preset standard
  python experiment_controller.py scalability --incremental --target_runs 2 --preset quick

  # 自定义筛选
  python experiment_controller.py attack --datasets MNIST CIFAR --attacks label_flipping backdoor
  python experiment_controller.py baseline --defenses martFL FLTrust --runs 3

  # 进度管理
  python experiment_controller.py attack --show_progress
  python experiment_controller.py baseline --force_restart
        """
    )
    
    parser.add_argument('experiment_type', 
                       choices=['baseline', 'attack', 'scalability', 'comprehensive', 'custom'],
                       help='实验类型')
    
    parser.add_argument('--output_dir', type=str, default='experiment_results',
                       help='实验输出目录 (默认: experiment_results)')
    
    parser.add_argument('--max_workers', type=int, default=None,
                       help='最大并行工作进程数 (默认: 自动检测)')
    
    parser.add_argument('--config_file', type=str, default=None,
                       help='自定义实验配置文件 (仅用于custom类型)')
    
    parser.add_argument('--no_analysis', action='store_true',
                       help='跳过最终分析')
    
    parser.add_argument('--verbose', action='store_true', default=True,
                       help='输出详细信息')
    
    parser.add_argument('--quiet', action='store_true',
                       help='静默模式')

    # === 实验筛选参数 ===
    parser.add_argument('--datasets', nargs='+',
                       choices=['MNIST', 'CIFAR', 'TREC'],
                       help='指定数据集 (例: --datasets MNIST CIFAR)')
    parser.add_argument('--attacks', nargs='+',
                       choices=['free_rider', 'rescaling', 'sybil', 'label_flipping', 'backdoor'],
                       help='指定攻击类型 (例: --attacks free_rider backdoor)')
    parser.add_argument('--defenses', nargs='+',
                       choices=['FedAvg', 'martFL', 'Krum', 'FLTrust', 'FLAME', 'BayBFed', 'FedRoLA', 'SDEAHFL'],
                       help='指定防御方法 (例: --defenses martFL FLTrust FLAME)')
    parser.add_argument('--participants', nargs='+', type=int,
                       choices=[10, 20, 50, 100, 200],
                       help='指定参与者数量 (例: --participants 10 20)')
    parser.add_argument('--adversary_ratios', nargs='+', type=float,
                       choices=[0.1, 0.2, 0.3],
                       help='指定恶意参与者比例 (例: --adversary_ratios 0.1 0.2)')
    parser.add_argument('--runs', type=int, default=None,
                       help='指定重复次数 (例: --runs 3)')
    parser.add_argument('--enable_pol', choices=['true', 'false', 'both'], default='both',
                       help='是否启用PoL (true/false/both)')

    # === 快速预设参数 ===
    parser.add_argument('--preset', choices=['quick', 'standard', 'full', 'debug'],
                       help='使用预设配置 (quick: 快速测试, standard: 标准实验, full: 完整实验, debug: 调试模式)')

    # === 增量式重复实验参数 ===
    parser.add_argument('--incremental', action='store_true',
                       help='启用增量式重复实验模式')
    parser.add_argument('--target_runs', type=int, default=None,
                       help='目标重复次数 (增量模式下使用)')
    parser.add_argument('--force_restart', action='store_true',
                       help='强制重新开始实验 (清除已有结果)')
    parser.add_argument('--show_progress', action='store_true',
                       help='显示当前实验进度')

    return parser

def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    # 设置详细程度
    verbose = args.verbose and not args.quiet

    # 处理预设配置
    if args.preset:
        args = apply_preset_config(args)

    # 创建实验控制器
    controller = ExperimentController(
        output_dir=args.output_dir,
        max_workers=args.max_workers,
        verbose=verbose
    )

    # 设置实验筛选参数
    controller.set_experiment_filters(args)

    # 处理特殊命令
    if args.show_progress:
        controller.show_experiment_progress()
        return

    if args.force_restart:
        controller.incremental_manager.clear_experiment_state()
        print("🗑️ 已清除所有实验状态，将重新开始实验")

    if verbose:
        print(f"🎯 实验类型: {args.experiment_type}")
        print(f"📁 输出目录: {args.output_dir}")
        print(f"⚙️ 并行进程: {controller.parallel_executor.max_workers}")
        if args.incremental:
            print(f"🔄 增量式模式: 目标 {controller.target_runs} 轮重复")
        print("="*80 + "\n")

    try:
        # 根据实验类型执行
        if args.experiment_type == 'baseline':
            results = controller.run_baseline_experiments()
        elif args.experiment_type == 'attack':
            results = controller.run_attack_experiments()
        elif args.experiment_type == 'scalability':
            results = controller.run_scalability_experiments()
        elif args.experiment_type == 'comprehensive':
            results = controller.run_comprehensive_experiments()
        elif args.experiment_type == 'custom':
            if not args.config_file:
                print("错误: custom类型需要指定 --config_file")
                sys.exit(1)
            
            # 加载自定义配置
            with open(args.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            experiments = [ExperimentConfig(**config) for config in config_data]
            results = controller.run_custom_experiments(experiments)
        
        # 生成最终分析
        if not args.no_analysis:
            controller.generate_final_analysis()
        
        print(f"\n🎉 实验完成！结果保存在: {args.output_dir}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 实验被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 实验执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
