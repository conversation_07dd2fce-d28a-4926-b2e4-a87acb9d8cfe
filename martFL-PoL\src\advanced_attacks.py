#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级攻击实现 - 补充顶会标准所需的攻击类型
包含自适应攻击、模型替换、梯度反演、成员推理等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from copy import deepcopy
import random
from collections import OrderedDict

class AdaptiveAttack:
    """
    自适应攻击 - 根据聚合器类型调整攻击策略
    针对不同的防御机制设计特定的攻击方法
    """
    
    def __init__(self, aggregator_type: str = 'fedavg', attack_strength: float = 1.0):
        self.aggregator_type = aggregator_type.lower()
        self.attack_strength = attack_strength
        self.attack_history = []
    
    def generate_malicious_update(self, benign_updates: List[OrderedDict], 
                                global_model: OrderedDict, round_num: int) -> OrderedDict:
        """生成自适应的恶意更新"""
        
        if self.aggregator_type == 'fedavg':
            return self._attack_fedavg(benign_updates, global_model)
        elif self.aggregator_type == 'krum':
            return self._attack_krum(benign_updates, global_model)
        elif self.aggregator_type == 'trimmed_mean':
            return self._attack_trimmed_mean(benign_updates, global_model)
        elif self.aggregator_type == 'martfl':
            return self._attack_martfl(benign_updates, global_model, round_num)
        else:
            # 默认攻击策略
            return self._default_attack(benign_updates, global_model)
    
    def _attack_fedavg(self, benign_updates: List[OrderedDict], global_model: OrderedDict) -> OrderedDict:
        """针对FedAvg的攻击"""
        if not benign_updates:
            return global_model
        
        # 计算良性更新的平均值
        avg_update = self._compute_average_update(benign_updates)
        
        # 生成反向更新
        malicious_update = OrderedDict()
        for key in avg_update.keys():
            # 反向攻击：与良性更新方向相反
            malicious_update[key] = global_model[key] - self.attack_strength * avg_update[key]
        
        return malicious_update
    
    def _attack_krum(self, benign_updates: List[OrderedDict], global_model: OrderedDict) -> OrderedDict:
        """针对Krum的攻击"""
        if len(benign_updates) < 2:
            return global_model
        
        # Krum选择距离其他更新最近的更新
        # 攻击策略：模仿最相似的两个良性更新
        similarities = self._compute_update_similarities(benign_updates)
        
        # 找到最相似的两个更新
        max_sim_idx = np.unravel_index(np.argmax(similarities), similarities.shape)
        if max_sim_idx[0] == max_sim_idx[1]:
            # 找到第二大相似度
            similarities[max_sim_idx] = -np.inf
            max_sim_idx = np.unravel_index(np.argmax(similarities), similarities.shape)
        
        # 生成介于两个最相似更新之间的恶意更新
        update1 = benign_updates[max_sim_idx[0]]
        update2 = benign_updates[max_sim_idx[1]]
        
        malicious_update = OrderedDict()
        for key in update1.keys():
            # 在两个相似更新之间插值，但添加微小扰动
            interpolated = 0.5 * (update1[key] + update2[key])
            noise = torch.randn_like(interpolated) * 0.01 * self.attack_strength
            malicious_update[key] = interpolated + noise
        
        return malicious_update
    
    def _attack_trimmed_mean(self, benign_updates: List[OrderedDict], global_model: OrderedDict) -> OrderedDict:
        """针对Trimmed Mean的攻击"""
        if not benign_updates:
            return global_model
        
        # Trimmed Mean去除极值
        # 攻击策略：生成接近中位数的更新
        median_update = self._compute_median_update(benign_updates)
        
        malicious_update = OrderedDict()
        for key in median_update.keys():
            # 在中位数附近添加恶意扰动
            perturbation = torch.randn_like(median_update[key]) * 0.1 * self.attack_strength
            malicious_update[key] = median_update[key] + perturbation
        
        return malicious_update
    
    def _attack_martfl(self, benign_updates: List[OrderedDict], global_model: OrderedDict, round_num: int) -> OrderedDict:
        """针对martFL的攻击"""
        # martFL使用马尔可夫链理论
        # 攻击策略：破坏马尔可夫性质
        
        if not benign_updates:
            return global_model
        
        # 分析历史攻击效果，调整策略
        if len(self.attack_history) > 3:
            # 使用历史信息调整攻击
            recent_attacks = self.attack_history[-3:]
            avg_recent = self._compute_average_update(recent_attacks)
            
            malicious_update = OrderedDict()
            for key in global_model.keys():
                # 基于历史模式生成攻击
                pattern_based = avg_recent[key] if key in avg_recent else torch.zeros_like(global_model[key])
                noise = torch.randn_like(global_model[key]) * 0.05 * self.attack_strength
                malicious_update[key] = global_model[key] + pattern_based + noise
        else:
            # 初期使用随机攻击
            malicious_update = self._default_attack(benign_updates, global_model)
        
        # 记录攻击历史
        self.attack_history.append(deepcopy(malicious_update))
        if len(self.attack_history) > 10:
            self.attack_history.pop(0)
        
        return malicious_update
    
    def _default_attack(self, benign_updates: List[OrderedDict], global_model: OrderedDict) -> OrderedDict:
        """默认攻击策略"""
        malicious_update = OrderedDict()
        for key in global_model.keys():
            # 添加高斯噪声
            noise = torch.randn_like(global_model[key]) * 0.1 * self.attack_strength
            malicious_update[key] = global_model[key] + noise
        
        return malicious_update
    
    def _compute_average_update(self, updates: List[OrderedDict]) -> OrderedDict:
        """计算更新的平均值"""
        if not updates:
            return OrderedDict()
        
        avg_update = OrderedDict()
        for key in updates[0].keys():
            avg_update[key] = torch.zeros_like(updates[0][key])
            for update in updates:
                avg_update[key] += update[key]
            avg_update[key] /= len(updates)
        
        return avg_update
    
    def _compute_median_update(self, updates: List[OrderedDict]) -> OrderedDict:
        """计算更新的中位数"""
        if not updates:
            return OrderedDict()
        
        median_update = OrderedDict()
        for key in updates[0].keys():
            # 收集所有更新在该参数上的值
            param_values = torch.stack([update[key] for update in updates])
            # 计算中位数
            median_update[key] = torch.median(param_values, dim=0)[0]
        
        return median_update
    
    def _compute_update_similarities(self, updates: List[OrderedDict]) -> np.ndarray:
        """计算更新之间的相似度矩阵"""
        n_updates = len(updates)
        similarities = np.zeros((n_updates, n_updates))
        
        # 将更新展平为向量
        vectors = []
        for update in updates:
            vector = torch.cat([param.flatten() for param in update.values()])
            vectors.append(vector)
        
        # 计算余弦相似度
        for i in range(n_updates):
            for j in range(n_updates):
                if i == j:
                    similarities[i, j] = 1.0
                else:
                    cos_sim = F.cosine_similarity(vectors[i], vectors[j], dim=0)
                    similarities[i, j] = cos_sim.item()
        
        return similarities

class ModelReplacementAttack:
    """
    模型替换攻击 - 用预训练的恶意模型替换正常更新
    """
    
    def __init__(self, target_class: int = 0, replacement_accuracy: float = 0.1):
        self.target_class = target_class
        self.replacement_accuracy = replacement_accuracy
        self.malicious_model = None
    
    def initialize_malicious_model(self, model_structure: nn.Module, device: torch.device):
        """初始化恶意模型"""
        self.malicious_model = deepcopy(model_structure)
        
        # 将所有参数设置为小的随机值，降低准确率
        with torch.no_grad():
            for param in self.malicious_model.parameters():
                param.data = torch.randn_like(param) * 0.01
    
    def generate_malicious_update(self, global_model: OrderedDict) -> OrderedDict:
        """生成模型替换攻击"""
        if self.malicious_model is None:
            # 如果没有预训练的恶意模型，生成随机模型
            malicious_update = OrderedDict()
            for key, param in global_model.items():
                malicious_update[key] = torch.randn_like(param) * 0.1
            return malicious_update
        
        # 返回恶意模型的参数
        malicious_update = OrderedDict()
        for (key, param), (_, malicious_param) in zip(global_model.items(), 
                                                     self.malicious_model.named_parameters()):
            malicious_update[key] = malicious_param.data.clone()
        
        return malicious_update

class GradientInversionAttack:
    """
    梯度反演攻击 - 从梯度信息推断训练数据
    """
    
    def __init__(self, num_iterations: int = 100, learning_rate: float = 0.1):
        self.num_iterations = num_iterations
        self.learning_rate = learning_rate
    
    def invert_gradients(self, gradients: OrderedDict, model: nn.Module, 
                        input_shape: Tuple, num_classes: int, device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """从梯度反演训练数据"""
        
        # 初始化虚假数据和标签
        dummy_data = torch.randn(input_shape, device=device, requires_grad=True)
        dummy_label = torch.randint(0, num_classes, (input_shape[0],), device=device, requires_grad=False)
        
        # 优化器
        optimizer = torch.optim.Adam([dummy_data], lr=self.learning_rate)
        
        # 目标梯度
        target_gradients = [grad.clone() for grad in gradients.values()]
        
        for iteration in range(self.num_iterations):
            optimizer.zero_grad()
            
            # 前向传播
            dummy_pred = model(dummy_data)
            dummy_loss = F.cross_entropy(dummy_pred, dummy_label)
            
            # 计算梯度
            dummy_gradients = torch.autograd.grad(dummy_loss, model.parameters(), create_graph=True)
            
            # 计算梯度差异
            grad_diff = 0
            for dummy_grad, target_grad in zip(dummy_gradients, target_gradients):
                grad_diff += ((dummy_grad - target_grad) ** 2).sum()
            
            # 反向传播
            grad_diff.backward()
            optimizer.step()
            
            # 数据范围约束
            with torch.no_grad():
                dummy_data.clamp_(0, 1)
        
        return dummy_data.detach(), dummy_label

class MembershipInferenceAttack:
    """
    成员推理攻击 - 推断特定数据是否在训练集中
    """
    
    def __init__(self, shadow_model_count: int = 5):
        self.shadow_model_count = shadow_model_count
        self.attack_model = None
        self.shadow_models = []
    
    def train_shadow_models(self, model_structure: nn.Module, shadow_datasets: List, device: torch.device):
        """训练影子模型"""
        self.shadow_models = []
        
        for i in range(self.shadow_model_count):
            shadow_model = deepcopy(model_structure).to(device)
            
            # 使用影子数据集训练模型
            if i < len(shadow_datasets):
                self._train_model(shadow_model, shadow_datasets[i], device)
            
            self.shadow_models.append(shadow_model)
    
    def train_attack_model(self, target_model: nn.Module, member_data: torch.Tensor, 
                          non_member_data: torch.Tensor, device: torch.device):
        """训练攻击模型"""
        # 简化的攻击模型：基于预测置信度的阈值分类器
        member_confidences = self._get_prediction_confidences(target_model, member_data, device)
        non_member_confidences = self._get_prediction_confidences(target_model, non_member_data, device)
        
        # 计算最优阈值
        all_confidences = torch.cat([member_confidences, non_member_confidences])
        all_labels = torch.cat([torch.ones(len(member_confidences)), torch.zeros(len(non_member_confidences))])
        
        best_threshold = 0.5
        best_accuracy = 0
        
        for threshold in torch.linspace(0, 1, 100):
            predictions = (all_confidences > threshold).float()
            accuracy = (predictions == all_labels).float().mean()
            
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_threshold = threshold
        
        self.attack_threshold = best_threshold.item()
    
    def infer_membership(self, target_model: nn.Module, query_data: torch.Tensor, device: torch.device) -> torch.Tensor:
        """推断查询数据的成员身份"""
        confidences = self._get_prediction_confidences(target_model, query_data, device)
        predictions = (confidences > self.attack_threshold).float()
        return predictions
    
    def _get_prediction_confidences(self, model: nn.Module, data: torch.Tensor, device: torch.device) -> torch.Tensor:
        """获取预测置信度"""
        model.eval()
        with torch.no_grad():
            outputs = model(data.to(device))
            probabilities = F.softmax(outputs, dim=1)
            confidences = torch.max(probabilities, dim=1)[0]
        return confidences
    
    def _train_model(self, model: nn.Module, dataset, device: torch.device, epochs: int = 10):
        """训练模型（简化版本）"""
        model.train()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        for epoch in range(epochs):
            for batch_data, batch_labels in dataset:
                batch_data, batch_labels = batch_data.to(device), batch_labels.to(device)
                
                optimizer.zero_grad()
                outputs = model(batch_data)
                loss = criterion(outputs, batch_labels)
                loss.backward()
                optimizer.step()

class PropertyInferenceAttack:
    """
    属性推理攻击 - 推断训练数据的统计属性
    """
    
    def __init__(self, target_property: str = 'class_distribution'):
        self.target_property = target_property
        self.property_classifier = None
    
    def infer_property(self, model: nn.Module, reference_models: List[nn.Module], device: torch.device) -> Dict[str, Any]:
        """推断目标属性"""
        
        if self.target_property == 'class_distribution':
            return self._infer_class_distribution(model, reference_models, device)
        elif self.target_property == 'data_size':
            return self._infer_data_size(model, reference_models, device)
        else:
            raise ValueError(f"不支持的属性类型: {self.target_property}")
    
    def _infer_class_distribution(self, model: nn.Module, reference_models: List[nn.Module], device: torch.device) -> Dict[str, Any]:
        """推断类别分布"""
        # 分析模型参数的统计特征
        target_features = self._extract_model_features(model)
        
        # 与参考模型比较
        reference_features = [self._extract_model_features(ref_model) for ref_model in reference_models]
        
        # 简化的推理：基于参数相似度
        similarities = []
        for ref_features in reference_features:
            similarity = F.cosine_similarity(target_features, ref_features, dim=0)
            similarities.append(similarity.item())
        
        # 根据相似度推断类别分布
        most_similar_idx = np.argmax(similarities)
        
        return {
            'inferred_distribution': f'类似于参考模型 {most_similar_idx}',
            'confidence': similarities[most_similar_idx],
            'similarities': similarities
        }
    
    def _infer_data_size(self, model: nn.Module, reference_models: List[nn.Module], device: torch.device) -> Dict[str, Any]:
        """推断数据集大小"""
        # 基于模型复杂度和参数范数推断
        target_norm = self._compute_model_norm(model)
        reference_norms = [self._compute_model_norm(ref_model) for ref_model in reference_models]
        
        # 简化的推理逻辑
        estimated_size = int(target_norm * 1000)  # 简化的映射关系
        
        return {
            'estimated_size': estimated_size,
            'target_norm': target_norm,
            'reference_norms': reference_norms
        }
    
    def _extract_model_features(self, model: nn.Module) -> torch.Tensor:
        """提取模型特征"""
        features = []
        for param in model.parameters():
            features.append(param.flatten())
        return torch.cat(features)
    
    def _compute_model_norm(self, model: nn.Module) -> float:
        """计算模型参数范数"""
        total_norm = 0
        for param in model.parameters():
            total_norm += param.norm().item() ** 2
        return total_norm ** 0.5

# 攻击工厂函数
def create_advanced_attack(attack_type: str, **kwargs):
    """创建高级攻击实例"""
    attack_map = {
        'adaptive': AdaptiveAttack,
        'model_replacement': ModelReplacementAttack,
        'gradient_inversion': GradientInversionAttack,
        'membership_inference': MembershipInferenceAttack,
        'property_inference': PropertyInferenceAttack
    }
    
    if attack_type.lower() not in attack_map:
        raise ValueError(f"不支持的攻击类型: {attack_type}")
    
    return attack_map[attack_type.lower()](**kwargs)
