import kmeans1d
import numpy as np
import pandas as pd
from gap_statistic import OptimalK
from sklearn.datasets import make_blobs
from sklearn.cluster import KMeans

def kmeans(x,k):

    clusters, centroids = kmeans1d.cluster(x, k)
    return clusters,centroids

def gap(x):
    """
    使用Gap统计量确定最优聚类数
    自动根据样本数量调整聚类范围
    """
    optimalK = OptimalK()

    # 根据样本数量动态确定聚类范围
    n_samples = len(x)
    max_clusters = min(4, max(1, n_samples - 1))  # 最多4个聚类，但不能超过样本数-1

    if max_clusters < 2:
        # 样本太少，直接返回1个聚类
        print(f"⚠️ 样本数量过少({n_samples})，使用单一聚类")
        return 1

    cluster_range = np.arange(1, max_clusters + 1)
    print(f"📊 尝试聚类范围: {cluster_range.tolist()} (样本数: {n_samples})")

    try:
        n_clusters = optimalK(x, cluster_array=cluster_range)
        return n_clusters
    except Exception as e:
        print(f"⚠️ Gap统计量计算失败: {e}")
        # 如果Gap统计量失败，使用简单的启发式方法
        return min(2, max_clusters)

if __name__ == '__main__':
    
    
    optimalK = OptimalK()

    x, y = make_blobs(n_samples=int(1e3), n_features=1, centers=3, random_state=25)
    print('Data shape: ', x.shape)

    n_clusters = optimalK(x, cluster_array=np.arange(1, 5))
    print('Optimal clusters: ', n_clusters)

    #print(optimalK.gap_df.head())
    
    #x = [4.0, 4.1, 4.2, -50, 200.2, 200.4, 200.9, 80, 100, 102]
    k = n_clusters
    clusters,centroids = kmeans(x,k)
    print(clusters)   # [1, 1, 1, 0, 3, 3, 3, 2, 2, 2]
    print(centroids)  # [-50.0, 4.1, 94.0, 200.5]
    