#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行执行器 - 高效并行实验执行
支持多GPU并行，智能任务调度，实时监控
"""

import subprocess
import threading
import queue
import time
import os
import signal
import psutil
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from pathlib import Path
import logging
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

from device_manager import get_device_manager
from experiment_manager import ExperimentConfig

@dataclass
class TaskResult:
    """任务执行结果"""
    experiment_id: str
    success: bool
    return_code: int
    stdout: str
    stderr: str
    execution_time: float
    device_used: str
    start_time: float
    end_time: float
    error_message: str = ""

class ProcessMonitor:
    """进程监控器"""
    
    def __init__(self, process: subprocess.Popen, experiment_id: str, device: str):
        self.process = process
        self.experiment_id = experiment_id
        self.device = device
        self.start_time = time.time()
        self.cpu_usage = []
        self.memory_usage = []
        self.gpu_usage = []
        self.monitoring = True
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_resources)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def _monitor_resources(self):
        """监控资源使用情况"""
        try:
            psutil_process = psutil.Process(self.process.pid)
            
            while self.monitoring and self.process.poll() is None:
                try:
                    # CPU和内存使用率
                    cpu_percent = psutil_process.cpu_percent()
                    memory_info = psutil_process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    
                    self.cpu_usage.append(cpu_percent)
                    self.memory_usage.append(memory_mb)
                    
                    # GPU使用率（如果使用GPU）
                    if 'cuda' in self.device:
                        # 这里可以添加GPU监控逻辑
                        pass
                    
                    time.sleep(5)  # 每5秒监控一次
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    break
                    
        except Exception as e:
            logging.warning(f"资源监控失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1)
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """获取资源使用摘要"""
        return {
            'avg_cpu_percent': sum(self.cpu_usage) / len(self.cpu_usage) if self.cpu_usage else 0,
            'max_cpu_percent': max(self.cpu_usage) if self.cpu_usage else 0,
            'avg_memory_mb': sum(self.memory_usage) / len(self.memory_usage) if self.memory_usage else 0,
            'max_memory_mb': max(self.memory_usage) if self.memory_usage else 0,
            'execution_time': time.time() - self.start_time
        }

class ParallelExecutor:
    """并行执行器"""
    
    def __init__(self, 
                 max_workers: Optional[int] = None,
                 output_dir: str = "experiment_results",
                 timeout_seconds: int = 3600,
                 verbose: bool = True):
        """
        初始化并行执行器
        
        Args:
            max_workers: 最大并行工作进程数
            output_dir: 结果输出目录
            timeout_seconds: 单个实验超时时间
            verbose: 是否输出详细信息
        """
        self.device_manager = get_device_manager(verbose=verbose)
        self.max_workers = max_workers or min(len(self.device_manager.gpu_info) or 1, 
                                            self.device_manager.system_info.get('cpu_physical_cores', 1))
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.timeout_seconds = timeout_seconds
        self.verbose = verbose
        
        self.logger = self._setup_logger()
        
        # 执行状态
        self.running_tasks: Dict[str, ProcessMonitor] = {}
        self.completed_tasks: List[TaskResult] = []
        self.failed_tasks: List[TaskResult] = []
        
        # 设备分配
        self.device_queue = queue.Queue()
        self._initialize_device_queue()
        
        # 统计信息
        self.start_time = 0
        self.total_tasks = 0
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ParallelExecutor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件输出
            log_file = self.output_dir / "executor.log"
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(console_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _initialize_device_queue(self):
        """初始化设备队列"""
        # 分配设备给工作进程
        devices = self.device_manager.allocate_devices(
            self.max_workers, 
            memory_per_process_gb=2.0
        )
        
        for device in devices:
            self.device_queue.put(device)
        
        self.logger.info(f"初始化了 {len(devices)} 个设备: {devices}")
    
    def _execute_single_experiment(self, config: ExperimentConfig) -> TaskResult:
        """执行单个实验"""
        # 获取设备
        device = self.device_queue.get()
        
        try:
            # 设置实验输出目录
            exp_output_dir = self.output_dir / f"exp_{config.experiment_id}"
            exp_output_dir.mkdir(exist_ok=True)
            
            # 构建命令
            cmd = ['python', 'main.py'] + config.to_args_list()
            if device != 'auto':
                cmd.extend(['--device', device])
            
            # 设置环境变量
            env = os.environ.copy()
            env['EXPERIMENT_ID'] = config.experiment_id
            env['EXPERIMENT_OUTPUT_DIR'] = str(exp_output_dir)
            
            # 如果使用特定GPU，设置CUDA_VISIBLE_DEVICES并调整设备参数
            if 'cuda:' in device:
                gpu_id = device.split(':')[1]
                env['CUDA_VISIBLE_DEVICES'] = gpu_id
                # 重要：当设置CUDA_VISIBLE_DEVICES后，实验内部应该使用cuda:0
                # 更新命令行参数中的设备设置
                for i, arg in enumerate(cmd):
                    if arg == '--device' and i + 1 < len(cmd):
                        cmd[i + 1] = 'cuda:0'  # 映射到可见设备的第一个GPU
                        break
            
            self.logger.info(f"开始实验 {config.experiment_id} (设备: {device})")
            
            # 启动进程
            start_time = time.time()
            # 确定正确的工作目录
            src_dir = Path(__file__).parent  # 当前文件所在的src目录
            process = subprocess.Popen(
                cmd,
                cwd=str(src_dir),  # 设置工作目录为src目录
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                preexec_fn=os.setsid if os.name == 'posix' else None
            )
            
            # 创建进程监控器
            monitor = ProcessMonitor(process, config.experiment_id, device)
            self.running_tasks[config.experiment_id] = monitor
            
            try:
                # 等待进程完成
                stdout, stderr = process.communicate(timeout=self.timeout_seconds)
                return_code = process.returncode
                
            except subprocess.TimeoutExpired:
                # 超时处理
                self.logger.warning(f"实验 {config.experiment_id} 超时，正在终止...")
                
                if os.name == 'posix':
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                else:
                    process.terminate()
                
                try:
                    stdout, stderr = process.communicate(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
                    stdout, stderr = process.communicate()
                
                return_code = -1
                stderr += "\n[TIMEOUT] 实验执行超时"
            
            finally:
                # 停止监控
                monitor.stop_monitoring()
                if config.experiment_id in self.running_tasks:
                    del self.running_tasks[config.experiment_id]
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 保存实验输出
            self._save_experiment_output(config, stdout, stderr, exp_output_dir)
            
            # 创建结果对象
            result = TaskResult(
                experiment_id=config.experiment_id,
                success=(return_code == 0),
                return_code=return_code,
                stdout=stdout,
                stderr=stderr,
                execution_time=execution_time,
                device_used=device,
                start_time=start_time,
                end_time=end_time,
                error_message=stderr if return_code != 0 else ""
            )
            
            # 记录结果
            if result.success:
                self.completed_tasks.append(result)
                self.logger.info(f"实验 {config.experiment_id} 完成 (耗时: {execution_time:.1f}s)")
            else:
                self.failed_tasks.append(result)
                self.logger.error(f"实验 {config.experiment_id} 失败 (返回码: {return_code})")
            
            return result
            
        except Exception as e:
            # 异常处理
            error_msg = f"实验执行异常: {str(e)}"
            self.logger.error(f"实验 {config.experiment_id} 异常: {error_msg}")
            
            result = TaskResult(
                experiment_id=config.experiment_id,
                success=False,
                return_code=-999,
                stdout="",
                stderr=error_msg,
                execution_time=0,
                device_used=device,
                start_time=time.time(),
                end_time=time.time(),
                error_message=error_msg
            )
            
            self.failed_tasks.append(result)
            return result
            
        finally:
            # 归还设备
            self.device_queue.put(device)
    
    def _save_experiment_output(self, config: ExperimentConfig, stdout: str, stderr: str, output_dir: Path):
        """保存实验输出"""
        # 保存配置
        with open(output_dir / "config.json", 'w', encoding='utf-8') as f:
            json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
        
        # 保存输出
        with open(output_dir / "stdout.log", 'w', encoding='utf-8') as f:
            f.write(stdout)
        
        with open(output_dir / "stderr.log", 'w', encoding='utf-8') as f:
            f.write(stderr)
    
    def execute_experiments(self, 
                          experiments: List[ExperimentConfig],
                          progress_callback: Optional[Callable] = None) -> List[TaskResult]:
        """
        并行执行实验列表
        
        Args:
            experiments: 实验配置列表
            progress_callback: 进度回调函数
            
        Returns:
            任务结果列表
        """
        self.total_tasks = len(experiments)
        self.start_time = time.time()
        
        self.logger.info(f"开始执行 {self.total_tasks} 个实验 (并行度: {self.max_workers})")
        
        # 清空之前的结果
        self.completed_tasks.clear()
        self.failed_tasks.clear()
        
        # 使用线程池执行
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_config = {
                executor.submit(self._execute_single_experiment, config): config 
                for config in experiments
            }
            
            # 收集结果
            results = []
            completed_count = 0
            
            for future in as_completed(future_to_config):
                config = future_to_config[future]
                
                try:
                    result = future.result()
                    results.append(result)
                    completed_count += 1
                    
                    # 进度回调
                    if progress_callback:
                        progress_callback(completed_count, self.total_tasks, result)
                    
                    # 打印进度
                    if self.verbose:
                        progress = completed_count / self.total_tasks * 100
                        elapsed = time.time() - self.start_time
                        eta = elapsed / completed_count * (self.total_tasks - completed_count) if completed_count > 0 else 0
                        
                        print(f"\r进度: {completed_count}/{self.total_tasks} ({progress:.1f}%) "
                              f"已用时: {elapsed:.0f}s 预计剩余: {eta:.0f}s", end='', flush=True)
                
                except Exception as e:
                    self.logger.error(f"任务执行异常: {e}")
        
        if self.verbose:
            print()  # 换行
        
        # 生成执行报告
        self._generate_execution_report()
        
        return results
    
    def _generate_execution_report(self):
        """生成执行报告"""
        total_time = time.time() - self.start_time
        success_count = len(self.completed_tasks)
        failed_count = len(self.failed_tasks)
        
        report = {
            'summary': {
                'total_experiments': self.total_tasks,
                'successful': success_count,
                'failed': failed_count,
                'success_rate': success_count / self.total_tasks if self.total_tasks > 0 else 0,
                'total_execution_time': total_time,
                'average_time_per_experiment': total_time / self.total_tasks if self.total_tasks > 0 else 0
            },
            'successful_experiments': [result.experiment_id for result in self.completed_tasks],
            'failed_experiments': [
                {
                    'experiment_id': result.experiment_id,
                    'error_message': result.error_message,
                    'return_code': result.return_code
                } for result in self.failed_tasks
            ],
            'device_usage': self._analyze_device_usage(),
            'timestamp': time.time()
        }
        
        # 保存报告
        report_file = self.output_dir / "execution_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        self.logger.info(f"执行完成！成功: {success_count}, 失败: {failed_count}, 总耗时: {total_time:.1f}s")
        self.logger.info(f"执行报告已保存到: {report_file}")
    
    def _analyze_device_usage(self) -> Dict[str, Any]:
        """分析设备使用情况"""
        device_stats = {}
        
        all_results = self.completed_tasks + self.failed_tasks
        for result in all_results:
            device = result.device_used
            if device not in device_stats:
                device_stats[device] = {
                    'total_experiments': 0,
                    'successful_experiments': 0,
                    'total_time': 0,
                    'average_time': 0
                }
            
            device_stats[device]['total_experiments'] += 1
            device_stats[device]['total_time'] += result.execution_time
            
            if result.success:
                device_stats[device]['successful_experiments'] += 1
        
        # 计算平均时间
        for device, stats in device_stats.items():
            if stats['total_experiments'] > 0:
                stats['average_time'] = stats['total_time'] / stats['total_experiments']
        
        return device_stats

if __name__ == "__main__":
    # 测试并行执行器
    from experiment_manager import ExperimentManager
    
    # 创建测试实验
    em = ExperimentManager()
    test_experiments = em.create_baseline_comparison_experiments()[:6]  # 只测试前6个
    
    # 创建并行执行器
    executor = ParallelExecutor(max_workers=2, verbose=True)
    
    # 执行实验
    results = executor.execute_experiments(test_experiments)
    
    print(f"\n执行完成！成功: {len(executor.completed_tasks)}, 失败: {len(executor.failed_tasks)}")
