#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备管理器 - 智能GPU/CPU检测和分配
支持多GPU环境，自动检测最优设备配置
"""

import torch
import psutil
import subprocess
import os
import logging
from typing import List, Dict, Tuple, Optional
import json
import time

class DeviceManager:
    """智能设备管理器"""
    
    def __init__(self, prefer_gpu: bool = True, verbose: bool = True):
        """
        初始化设备管理器
        
        Args:
            prefer_gpu: 是否优先使用GPU
            verbose: 是否输出详细信息
        """
        self.prefer_gpu = prefer_gpu
        self.verbose = verbose
        self.logger = self._setup_logger()
        
        # 检测系统配置
        self.system_info = self._detect_system()
        self.gpu_info = self._detect_gpus()
        self.cpu_info = self._detect_cpu()
        
        # 设备分配策略
        self.available_devices = self._get_available_devices()
        
        if self.verbose:
            self._print_system_summary()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('DeviceManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _detect_system(self) -> Dict:
        """检测系统基本信息"""
        try:
            # CPU信息
            cpu_count = psutil.cpu_count(logical=False)  # 物理核心
            cpu_count_logical = psutil.cpu_count(logical=True)  # 逻辑核心
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            
            # 系统信息
            system_info = {
                'cpu_physical_cores': cpu_count,
                'cpu_logical_cores': cpu_count_logical,
                'memory_total_gb': round(memory_gb, 2),
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'platform': os.name,
                'python_version': torch.__version__
            }
            
            return system_info
            
        except Exception as e:
            self.logger.warning(f"系统信息检测失败: {e}")
            return {}
    
    def _detect_gpus(self) -> List[Dict]:
        """检测GPU信息"""
        gpu_info = []

        if not torch.cuda.is_available():
            self.logger.info("CUDA不可用，将使用CPU")
            return gpu_info

        self.logger.info(f"CUDA可用，开始检测GPU...")

        try:
            gpu_count = torch.cuda.device_count()
            self.logger.info(f"检测到 {gpu_count} 个GPU")

            for i in range(gpu_count):
                try:
                    props = torch.cuda.get_device_properties(i)

                    # 获取GPU内存信息
                    torch.cuda.set_device(i)
                    memory_total = props.total_memory
                    memory_allocated = torch.cuda.memory_allocated(i)
                    memory_free = memory_total - memory_allocated

                    # 兼容不同PyTorch版本的属性名
                    multiprocessor_count = 0
                    try:
                        # 新版本PyTorch
                        multiprocessor_count = props.multiprocessor_count
                    except AttributeError:
                        try:
                            # 旧版本PyTorch可能使用multi_processor_count
                            multiprocessor_count = props.multi_processor_count
                        except AttributeError:
                            try:
                                # 更旧版本可能使用sm_count
                                multiprocessor_count = getattr(props, 'sm_count', 0)
                            except AttributeError:
                                # 如果都没有，设为0
                                multiprocessor_count = 0

                    gpu_info.append({
                        'id': i,
                        'name': props.name,
                        'compute_capability': f"{props.major}.{props.minor}",
                        'memory_total_gb': round(memory_total / (1024**3), 2),
                        'memory_free_gb': round(memory_free / (1024**3), 2),
                        'multiprocessor_count': multiprocessor_count,
                        'is_available': True
                    })

                    self.logger.info(f"GPU {i}: {props.name}")

                except Exception as gpu_error:
                    self.logger.warning(f"检测GPU {i} 时出错: {gpu_error}")
                    continue

            # 重置到默认设备
            if gpu_count > 0:
                torch.cuda.set_device(0)

        except Exception as e:
            self.logger.warning(f"GPU检测失败: {e}")
            # 如果是CPU版本的PyTorch，给出明确提示
            if "+cpu" in torch.__version__:
                self.logger.warning("检测到CPU版本的PyTorch，无法使用GPU。请安装GPU版本的PyTorch。")
            else:
                self.logger.warning("GPU检测失败，但PyTorch支持CUDA。可能是驱动或硬件问题。")

        return gpu_info
    
    def _detect_cpu(self) -> Dict:
        """检测CPU详细信息"""
        try:
            # 获取CPU频率
            cpu_freq = psutil.cpu_freq()
            
            # 获取CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            cpu_info = {
                'model': self._get_cpu_model(),
                'frequency_mhz': round(cpu_freq.current, 2) if cpu_freq else 0,
                'usage_percent': cpu_percent,
                'physical_cores': self.system_info.get('cpu_physical_cores', 0),
                'logical_cores': self.system_info.get('cpu_logical_cores', 0)
            }
            
            return cpu_info
            
        except Exception as e:
            self.logger.warning(f"CPU信息检测失败: {e}")
            return {}
    
    def _get_cpu_model(self) -> str:
        """获取CPU型号"""
        try:
            if os.name == 'posix':  # Linux/Unix
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if 'model name' in line:
                            return line.split(':')[1].strip()
            return "Unknown CPU"
        except:
            return "Unknown CPU"
    
    def _get_available_devices(self) -> List[str]:
        """获取可用设备列表"""
        devices = []
        
        # 添加GPU设备
        if self.prefer_gpu and self.gpu_info:
            for gpu in self.gpu_info:
                if gpu['is_available']:
                    devices.append(f"cuda:{gpu['id']}")
        
        # 添加CPU设备
        devices.append('cpu')
        
        return devices
    
    def get_optimal_device(self, memory_requirement_gb: float = 2.0) -> str:
        """
        获取最优设备
        
        Args:
            memory_requirement_gb: 内存需求(GB)
            
        Returns:
            设备字符串 (如 'cuda:0' 或 'cpu')
        """
        if not self.prefer_gpu or not self.gpu_info:
            return 'cpu'
        
        # 选择内存最充足的GPU
        best_gpu = None
        max_free_memory = 0
        
        for gpu in self.gpu_info:
            if gpu['memory_free_gb'] >= memory_requirement_gb:
                if gpu['memory_free_gb'] > max_free_memory:
                    max_free_memory = gpu['memory_free_gb']
                    best_gpu = gpu
        
        if best_gpu:
            return f"cuda:{best_gpu['id']}"
        else:
            self.logger.warning(f"GPU内存不足({memory_requirement_gb}GB)，使用CPU")
            return 'cpu'
    
    def allocate_devices(self, num_processes: int, memory_per_process_gb: float = 2.0) -> List[str]:
        """
        为多个进程分配设备
        
        Args:
            num_processes: 进程数量
            memory_per_process_gb: 每个进程的内存需求
            
        Returns:
            设备分配列表
        """
        allocated_devices = []
        
        # 可用GPU数量
        available_gpus = [gpu for gpu in self.gpu_info 
                         if gpu['memory_free_gb'] >= memory_per_process_gb]
        
        # 分配策略：优先使用GPU，然后使用CPU
        for i in range(num_processes):
            if i < len(available_gpus) and self.prefer_gpu:
                allocated_devices.append(f"cuda:{available_gpus[i]['id']}")
            else:
                allocated_devices.append('cpu')
        
        return allocated_devices
    
    def _print_system_summary(self):
        """打印系统配置摘要"""
        print("\n" + "="*60)
        print("🖥️  系统配置检测结果")
        print("="*60)
        
        # CPU信息
        cpu_model = self.cpu_info.get('model', 'Unknown')
        cpu_cores = f"{self.system_info.get('cpu_physical_cores', 0)}核心/{self.system_info.get('cpu_logical_cores', 0)}线程"
        print(f"💻 CPU: {cpu_model}")
        print(f"   核心: {cpu_cores}")
        print(f"   频率: {self.cpu_info.get('frequency_mhz', 0):.0f} MHz")
        print(f"   使用率: {self.cpu_info.get('usage_percent', 0):.1f}%")
        
        # 内存信息
        memory_total = self.system_info.get('memory_total_gb', 0)
        memory_available = self.system_info.get('memory_available_gb', 0)
        print(f"🧠 内存: {memory_total:.1f}GB 总计, {memory_available:.1f}GB 可用")
        
        # GPU信息
        if self.gpu_info:
            print(f"🎮 GPU: 检测到 {len(self.gpu_info)} 个GPU")
            for gpu in self.gpu_info:
                print(f"   GPU {gpu['id']}: {gpu['name']}")
                print(f"   显存: {gpu['memory_free_gb']:.1f}GB 可用 / {gpu['memory_total_gb']:.1f}GB 总计")
                print(f"   计算能力: {gpu['compute_capability']}")
        else:
            print("🎮 GPU: 未检测到可用GPU")
        
        # 推荐配置
        print(f"\n🚀 推荐配置:")
        optimal_device = self.get_optimal_device()
        print(f"   主设备: {optimal_device}")
        
        if len(self.gpu_info) > 1:
            max_parallel = min(len(self.gpu_info), self.system_info.get('cpu_physical_cores', 1))
            print(f"   并行进程: 最多 {max_parallel} 个")
        
        print("="*60 + "\n")
    
    def save_device_info(self, filepath: str):
        """保存设备信息到文件"""
        device_info = {
            'system_info': self.system_info,
            'cpu_info': self.cpu_info,
            'gpu_info': self.gpu_info,
            'available_devices': self.available_devices,
            'timestamp': time.time()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(device_info, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"设备信息已保存到: {filepath}")

# 全局设备管理器实例
_device_manager = None

def get_device_manager(prefer_gpu: bool = True, verbose: bool = True) -> DeviceManager:
    """获取全局设备管理器实例"""
    global _device_manager
    if _device_manager is None:
        _device_manager = DeviceManager(prefer_gpu=prefer_gpu, verbose=verbose)
    return _device_manager

def get_optimal_device(memory_requirement_gb: float = 2.0) -> str:
    """快速获取最优设备"""
    dm = get_device_manager(verbose=False)
    return dm.get_optimal_device(memory_requirement_gb)

if __name__ == "__main__":
    # 测试设备管理器
    dm = DeviceManager(verbose=True)
    
    # 保存设备信息
    dm.save_device_info("device_info.json")
    
    # 测试设备分配
    print("\n测试设备分配:")
    devices = dm.allocate_devices(4, memory_per_process_gb=1.5)
    for i, device in enumerate(devices):
        print(f"进程 {i}: {device}")
