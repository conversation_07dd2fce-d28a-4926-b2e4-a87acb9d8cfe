"""
增量式实验管理器

支持分多次运行实验，自动管理重复次数和实验进度
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import asdict
import hashlib
from datetime import datetime

from experiment_manager import ExperimentConfig

class IncrementalExperimentManager:
    """增量式实验管理器"""
    
    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 实验状态文件
        self.state_file = self.output_dir / "experiment_state.json"
        self.progress_file = self.output_dir / "experiment_progress.json"
        
        self.logger = logging.getLogger('IncrementalExperimentManager')
        
        # 加载已有状态
        self.experiment_state = self._load_experiment_state()
        self.experiment_progress = self._load_experiment_progress()
    
    def _load_experiment_state(self) -> Dict:
        """加载实验状态"""
        if self.state_file.exists():
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载实验状态失败: {e}")
        
        return {
            'experiment_signature': None,
            'target_runs': 0,
            'completed_runs': 0,
            'experiment_configs': [],
            'created_time': None,
            'last_update': None
        }
    
    def _load_experiment_progress(self) -> Dict:
        """加载实验进度"""
        if self.progress_file.exists():
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载实验进度失败: {e}")
        
        return {}
    
    def _save_experiment_state(self):
        """保存实验状态"""
        try:
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(self.experiment_state, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存实验状态失败: {e}")
    
    def _save_experiment_progress(self):
        """保存实验进度"""
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.experiment_progress, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存实验进度失败: {e}")
    
    def _generate_experiment_signature(self, base_configs: List[ExperimentConfig]) -> str:
        """生成实验配置的唯一签名"""
        # 创建基础配置的签名（不包括run_id）
        signature_data = []
        for config in base_configs:
            config_dict = asdict(config)
            # 移除run_id和时间戳等变化的字段
            config_dict.pop('run_id', None)
            config_dict.pop('timestamp', None)
            config_dict.pop('experiment_id', None)
            signature_data.append(sorted(config_dict.items()))
        
        # 生成MD5签名
        signature_str = str(signature_data)
        return hashlib.md5(signature_str.encode()).hexdigest()
    
    def _extract_base_configs(self, experiments: List[ExperimentConfig]) -> List[ExperimentConfig]:
        """提取基础配置（run_id=1的配置）"""
        base_configs = []
        seen_configs = set()
        
        for exp in experiments:
            # 创建配置的唯一标识（不包括run_id）
            config_key = (
                exp.dataset, exp.aggregator, exp.attack, exp.n_participant, 
                exp.n_adversary, exp.enable_pol
            )
            
            if config_key not in seen_configs:
                # 创建基础配置（run_id=1）
                base_config = ExperimentConfig(**asdict(exp))
                base_config.run_id = 1
                base_configs.append(base_config)
                seen_configs.add(config_key)
        
        return base_configs
    
    def prepare_incremental_experiments(self, 
                                      experiments: List[ExperimentConfig],
                                      target_runs: int,
                                      force_restart: bool = False) -> Tuple[List[ExperimentConfig], Dict]:
        """
        准备增量式实验
        
        Args:
            experiments: 原始实验配置列表
            target_runs: 目标重复次数
            force_restart: 是否强制重新开始
            
        Returns:
            (需要运行的实验配置, 状态信息)
        """
        # 提取基础配置
        base_configs = self._extract_base_configs(experiments)
        current_signature = self._generate_experiment_signature(base_configs)
        
        # 检查是否是新的实验配置
        is_new_experiment = (
            force_restart or 
            self.experiment_state['experiment_signature'] != current_signature
        )
        
        if is_new_experiment:
            # 新实验或强制重启
            self.logger.info("🆕 检测到新的实验配置或强制重启")
            self.experiment_state = {
                'experiment_signature': current_signature,
                'target_runs': target_runs,
                'completed_runs': 0,
                'experiment_configs': [asdict(config) for config in base_configs],
                'created_time': datetime.now().isoformat(),
                'last_update': datetime.now().isoformat()
            }
            self.experiment_progress = {}
            
            # 生成第一轮实验
            experiments_to_run = []
            for base_config in base_configs:
                config = ExperimentConfig(**asdict(base_config))
                config.run_id = 1
                experiments_to_run.append(config)
            
            next_run = 1
            
        else:
            # 继续已有实验
            completed_runs = self.experiment_state['completed_runs']
            
            if completed_runs >= target_runs:
                self.logger.info(f"✅ 实验已完成 {completed_runs}/{target_runs} 轮")
                return [], {
                    'status': 'completed',
                    'completed_runs': completed_runs,
                    'target_runs': target_runs,
                    'total_experiments': len(base_configs) * target_runs
                }
            
            # 生成下一轮实验
            next_run = completed_runs + 1
            self.logger.info(f"🔄 继续实验，准备第 {next_run}/{target_runs} 轮")
            
            experiments_to_run = []
            for base_config_dict in self.experiment_state['experiment_configs']:
                config = ExperimentConfig(**base_config_dict)
                config.run_id = next_run
                experiments_to_run.append(config)
        
        # 保存状态
        self._save_experiment_state()
        self._save_experiment_progress()
        
        status_info = {
            'status': 'running',
            'current_run': next_run,
            'target_runs': target_runs,
            'completed_runs': self.experiment_state['completed_runs'],
            'total_experiments': len(base_configs) * target_runs,
            'experiments_this_run': len(experiments_to_run),
            'is_new_experiment': is_new_experiment
        }
        
        return experiments_to_run, status_info
    
    def mark_run_completed(self, run_id: int):
        """标记某轮实验完成"""
        if run_id > self.experiment_state['completed_runs']:
            self.experiment_state['completed_runs'] = run_id
            self.experiment_state['last_update'] = datetime.now().isoformat()
            self._save_experiment_state()
            
            self.logger.info(f"✅ 第 {run_id} 轮实验完成")
    
    def get_experiment_status(self) -> Dict:
        """获取实验状态"""
        if not self.experiment_state['experiment_signature']:
            return {
                'status': 'no_experiments',
                'message': '没有进行中的实验'
            }
        
        completed = self.experiment_state['completed_runs']
        target = self.experiment_state['target_runs']
        total_configs = len(self.experiment_state['experiment_configs'])
        
        return {
            'status': 'completed' if completed >= target else 'in_progress',
            'experiment_signature': self.experiment_state['experiment_signature'],
            'completed_runs': completed,
            'target_runs': target,
            'progress_percentage': (completed / target * 100) if target > 0 else 0,
            'total_base_configs': total_configs,
            'total_experiments': total_configs * target,
            'completed_experiments': total_configs * completed,
            'remaining_experiments': total_configs * (target - completed),
            'created_time': self.experiment_state['created_time'],
            'last_update': self.experiment_state['last_update']
        }
    
    def clear_experiment_state(self):
        """清除实验状态（强制重新开始）"""
        self.experiment_state = {
            'experiment_signature': None,
            'target_runs': 0,
            'completed_runs': 0,
            'experiment_configs': [],
            'created_time': None,
            'last_update': None
        }
        self.experiment_progress = {}
        
        # 删除状态文件
        if self.state_file.exists():
            self.state_file.unlink()
        if self.progress_file.exists():
            self.progress_file.unlink()
        
        self.logger.info("🗑️ 已清除所有实验状态")
