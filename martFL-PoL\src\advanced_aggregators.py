#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级聚合器实现 - 补充顶会标准所需的SOTA方法
包含FLAME, FedNova, SCAFFOLD, FedOpt, Trimmed-Mean等
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from copy import deepcopy
import math
from collections import OrderedDict

class FLAMEAggregator:
    """
    FLAME: Federated Learning with Adaptive Model Exchange
    基于余弦相似度的鲁棒聚合器
    """
    
    def __init__(self, damping_factor: float = 0.5, clip_threshold: float = 4.0):
        self.damping_factor = damping_factor
        self.clip_threshold = clip_threshold
        self.global_momentum = None
    
    def aggregate(self, models: List[OrderedDict], weights: Optional[List[float]] = None) -> OrderedDict:
        """FLAME聚合算法"""
        if not models:
            raise ValueError("模型列表不能为空")
        
        n_models = len(models)
        if weights is None:
            weights = [1.0 / n_models] * n_models
        
        # 计算模型更新
        updates = []
        for model in models:
            if self.global_momentum is None:
                # 第一轮，直接使用模型参数
                updates.append(model)
            else:
                # 计算更新差值
                update = OrderedDict()
                for key in model.keys():
                    update[key] = model[key] - self.global_momentum[key]
                updates.append(update)
        
        # 计算余弦相似度矩阵
        similarities = self._compute_cosine_similarities(updates)
        
        # 基于相似度调整权重
        adjusted_weights = self._adjust_weights_by_similarity(similarities, weights)
        
        # 聚合更新
        aggregated_update = self._weighted_average(updates, adjusted_weights)
        
        # 梯度裁剪
        aggregated_update = self._clip_gradients(aggregated_update)
        
        # 更新全局动量
        if self.global_momentum is None:
            self.global_momentum = deepcopy(aggregated_update)
        else:
            for key in aggregated_update.keys():
                self.global_momentum[key] = (
                    self.damping_factor * self.global_momentum[key] + 
                    (1 - self.damping_factor) * aggregated_update[key]
                )
        
        return self.global_momentum
    
    def _compute_cosine_similarities(self, updates: List[OrderedDict]) -> np.ndarray:
        """计算模型更新之间的余弦相似度"""
        n_models = len(updates)
        similarities = np.zeros((n_models, n_models))
        
        # 将模型参数展平为向量
        vectors = []
        for update in updates:
            vector = torch.cat([param.flatten() for param in update.values()])
            vectors.append(vector)
        
        # 计算余弦相似度
        for i in range(n_models):
            for j in range(n_models):
                if i == j:
                    similarities[i, j] = 1.0
                else:
                    cos_sim = torch.cosine_similarity(vectors[i], vectors[j], dim=0)
                    similarities[i, j] = cos_sim.item()
        
        return similarities
    
    def _adjust_weights_by_similarity(self, similarities: np.ndarray, weights: List[float]) -> List[float]:
        """基于相似度调整权重"""
        n_models = len(weights)
        adjusted_weights = []
        
        for i in range(n_models):
            # 计算与其他模型的平均相似度
            avg_similarity = np.mean([similarities[i, j] for j in range(n_models) if i != j])
            
            # 相似度越高，权重越大
            adjusted_weight = weights[i] * (1 + avg_similarity)
            adjusted_weights.append(adjusted_weight)
        
        # 归一化权重
        total_weight = sum(adjusted_weights)
        adjusted_weights = [w / total_weight for w in adjusted_weights]
        
        return adjusted_weights
    
    def _weighted_average(self, updates: List[OrderedDict], weights: List[float]) -> OrderedDict:
        """加权平均聚合"""
        if not updates:
            raise ValueError("更新列表不能为空")
        
        aggregated = OrderedDict()
        
        for key in updates[0].keys():
            aggregated[key] = torch.zeros_like(updates[0][key])
            for update, weight in zip(updates, weights):
                aggregated[key] += weight * update[key]
        
        return aggregated
    
    def _clip_gradients(self, update: OrderedDict) -> OrderedDict:
        """梯度裁剪"""
        clipped_update = OrderedDict()
        
        for key, param in update.items():
            norm = torch.norm(param)
            if norm > self.clip_threshold:
                clipped_update[key] = param * (self.clip_threshold / norm)
            else:
                clipped_update[key] = param
        
        return clipped_update

class FedNovaAggregator:
    """
    FedNova: Addressing Client Drift in Federated Optimization
    处理客户端漂移的联邦优化方法
    """
    
    def __init__(self, momentum: float = 0.9):
        self.momentum = momentum
        self.global_momentum = None
        self.step_counts = {}
    
    def aggregate(self, models: List[OrderedDict], step_counts: List[int], 
                 weights: Optional[List[float]] = None) -> OrderedDict:
        """FedNova聚合算法"""
        if not models:
            raise ValueError("模型列表不能为空")
        
        n_models = len(models)
        if weights is None:
            weights = [1.0 / n_models] * n_models
        
        # 计算有效步数
        effective_steps = self._compute_effective_steps(step_counts, weights)
        
        # 计算标准化的模型更新
        normalized_updates = []
        for i, (model, steps) in enumerate(zip(models, step_counts)):
            if self.global_momentum is None:
                # 第一轮
                normalized_update = model
            else:
                # 计算更新并标准化
                update = OrderedDict()
                for key in model.keys():
                    raw_update = model[key] - self.global_momentum[key]
                    # 根据步数标准化
                    normalized_update_val = raw_update * (effective_steps / steps) if steps > 0 else raw_update
                    update[key] = normalized_update_val
                normalized_update = update
            
            normalized_updates.append(normalized_update)
        
        # 加权聚合
        aggregated_update = self._weighted_average(normalized_updates, weights)
        
        # 更新全局动量
        if self.global_momentum is None:
            self.global_momentum = deepcopy(aggregated_update)
        else:
            for key in aggregated_update.keys():
                self.global_momentum[key] = (
                    self.momentum * self.global_momentum[key] + 
                    aggregated_update[key]
                )
        
        return self.global_momentum
    
    def _compute_effective_steps(self, step_counts: List[int], weights: List[float]) -> float:
        """计算有效步数"""
        weighted_steps = sum(steps * weight for steps, weight in zip(step_counts, weights))
        return weighted_steps
    
    def _weighted_average(self, updates: List[OrderedDict], weights: List[float]) -> OrderedDict:
        """加权平均聚合"""
        if not updates:
            raise ValueError("更新列表不能为空")
        
        aggregated = OrderedDict()
        
        for key in updates[0].keys():
            aggregated[key] = torch.zeros_like(updates[0][key])
            for update, weight in zip(updates, weights):
                aggregated[key] += weight * update[key]
        
        return aggregated

class SCAFFOLDAggregator:
    """
    SCAFFOLD: Stochastic Controlled Averaging for Federated Learning
    随机控制平均的联邦学习方法
    """
    
    def __init__(self, learning_rate: float = 1.0):
        self.learning_rate = learning_rate
        self.global_control = None
        self.client_controls = {}
    
    def aggregate(self, models: List[OrderedDict], client_controls: List[OrderedDict],
                 client_ids: List[int], weights: Optional[List[float]] = None) -> Tuple[OrderedDict, OrderedDict]:
        """SCAFFOLD聚合算法"""
        if not models:
            raise ValueError("模型列表不能为空")
        
        n_models = len(models)
        if weights is None:
            weights = [1.0 / n_models] * n_models
        
        # 初始化全局控制变量
        if self.global_control is None:
            self.global_control = OrderedDict()
            for key in models[0].keys():
                self.global_control[key] = torch.zeros_like(models[0][key])
        
        # 更新客户端控制变量
        for client_id, control in zip(client_ids, client_controls):
            self.client_controls[client_id] = control
        
        # 计算模型更新
        model_updates = []
        control_updates = []
        
        for i, (model, client_control, client_id) in enumerate(zip(models, client_controls, client_ids)):
            # 计算模型更新
            model_update = OrderedDict()
            for key in model.keys():
                if client_id in self.client_controls:
                    old_model = self.client_controls[client_id]
                    model_update[key] = model[key] - old_model.get(key, torch.zeros_like(model[key]))
                else:
                    model_update[key] = model[key]
            
            model_updates.append(model_update)
            control_updates.append(client_control)
        
        # 聚合模型更新
        aggregated_model = self._weighted_average(model_updates, weights)
        
        # 聚合控制变量更新
        aggregated_control = self._weighted_average(control_updates, weights)
        
        # 更新全局控制变量
        for key in self.global_control.keys():
            self.global_control[key] += aggregated_control[key]
        
        return aggregated_model, self.global_control
    
    def _weighted_average(self, updates: List[OrderedDict], weights: List[float]) -> OrderedDict:
        """加权平均聚合"""
        if not updates:
            raise ValueError("更新列表不能为空")
        
        aggregated = OrderedDict()
        
        for key in updates[0].keys():
            aggregated[key] = torch.zeros_like(updates[0][key])
            for update, weight in zip(updates, weights):
                aggregated[key] += weight * update[key]
        
        return aggregated

class TrimmedMeanAggregator:
    """
    Trimmed Mean聚合器
    通过去除极值来提高鲁棒性
    """
    
    def __init__(self, trim_ratio: float = 0.2):
        """
        Args:
            trim_ratio: 要去除的极值比例（每端）
        """
        self.trim_ratio = trim_ratio
    
    def aggregate(self, models: List[OrderedDict], weights: Optional[List[float]] = None) -> OrderedDict:
        """Trimmed Mean聚合算法"""
        if not models:
            raise ValueError("模型列表不能为空")
        
        n_models = len(models)
        if weights is None:
            weights = [1.0 / n_models] * n_models
        
        # 计算要去除的模型数量
        n_trim = int(n_models * self.trim_ratio)
        
        aggregated = OrderedDict()
        
        for key in models[0].keys():
            # 收集所有模型在该参数上的值
            param_values = []
            param_weights = []
            
            for model, weight in zip(models, weights):
                param_values.append(model[key])
                param_weights.append(weight)
            
            # 对参数值进行排序并去除极值
            trimmed_values, trimmed_weights = self._trim_values(param_values, param_weights, n_trim)
            
            # 计算加权平均
            if trimmed_values:
                total_weight = sum(trimmed_weights)
                aggregated[key] = torch.zeros_like(trimmed_values[0])
                
                for value, weight in zip(trimmed_values, trimmed_weights):
                    aggregated[key] += (weight / total_weight) * value
            else:
                # 如果所有值都被去除，使用原始平均
                aggregated[key] = torch.zeros_like(models[0][key])
                for model, weight in zip(models, weights):
                    aggregated[key] += weight * model[key]
        
        return aggregated
    
    def _trim_values(self, values: List[torch.Tensor], weights: List[float], 
                    n_trim: int) -> Tuple[List[torch.Tensor], List[float]]:
        """去除极值"""
        if n_trim <= 0 or n_trim >= len(values) // 2:
            return values, weights
        
        # 计算每个值的范数作为排序依据
        norms = [torch.norm(value).item() for value in values]
        
        # 创建索引并按范数排序
        indexed_data = list(zip(norms, values, weights, range(len(values))))
        indexed_data.sort(key=lambda x: x[0])
        
        # 去除最小和最大的n_trim个值
        trimmed_data = indexed_data[n_trim:-n_trim] if n_trim > 0 else indexed_data
        
        # 提取去除极值后的值和权重
        trimmed_values = [item[1] for item in trimmed_data]
        trimmed_weights = [item[2] for item in trimmed_data]
        
        return trimmed_values, trimmed_weights

class FedOptAggregator:
    """
    FedOpt: Adaptive Federated Optimization
    自适应联邦优化方法，支持多种优化器
    """
    
    def __init__(self, optimizer_type: str = 'adam', learning_rate: float = 0.01,
                 beta1: float = 0.9, beta2: float = 0.999, epsilon: float = 1e-8):
        self.optimizer_type = optimizer_type.lower()
        self.learning_rate = learning_rate
        self.beta1 = beta1
        self.beta2 = beta2
        self.epsilon = epsilon
        
        # 优化器状态
        self.momentum = None
        self.velocity = None
        self.step_count = 0
    
    def aggregate(self, models: List[OrderedDict], weights: Optional[List[float]] = None) -> OrderedDict:
        """FedOpt聚合算法"""
        if not models:
            raise ValueError("模型列表不能为空")
        
        n_models = len(models)
        if weights is None:
            weights = [1.0 / n_models] * n_models
        
        # 计算加权平均更新
        aggregated_update = self._weighted_average(models, weights)
        
        # 应用优化器
        if self.optimizer_type == 'sgd':
            optimized_update = self._apply_sgd(aggregated_update)
        elif self.optimizer_type == 'momentum':
            optimized_update = self._apply_momentum(aggregated_update)
        elif self.optimizer_type == 'adam':
            optimized_update = self._apply_adam(aggregated_update)
        else:
            raise ValueError(f"不支持的优化器类型: {self.optimizer_type}")
        
        self.step_count += 1
        return optimized_update
    
    def _weighted_average(self, models: List[OrderedDict], weights: List[float]) -> OrderedDict:
        """加权平均聚合"""
        aggregated = OrderedDict()
        
        for key in models[0].keys():
            aggregated[key] = torch.zeros_like(models[0][key])
            for model, weight in zip(models, weights):
                aggregated[key] += weight * model[key]
        
        return aggregated
    
    def _apply_sgd(self, update: OrderedDict) -> OrderedDict:
        """应用SGD优化器"""
        optimized = OrderedDict()
        for key, param in update.items():
            optimized[key] = param - self.learning_rate * param
        return optimized
    
    def _apply_momentum(self, update: OrderedDict) -> OrderedDict:
        """应用动量优化器"""
        if self.momentum is None:
            self.momentum = OrderedDict()
            for key in update.keys():
                self.momentum[key] = torch.zeros_like(update[key])
        
        optimized = OrderedDict()
        for key, param in update.items():
            self.momentum[key] = self.beta1 * self.momentum[key] + (1 - self.beta1) * param
            optimized[key] = param - self.learning_rate * self.momentum[key]
        
        return optimized
    
    def _apply_adam(self, update: OrderedDict) -> OrderedDict:
        """应用Adam优化器"""
        if self.momentum is None:
            self.momentum = OrderedDict()
            self.velocity = OrderedDict()
            for key in update.keys():
                self.momentum[key] = torch.zeros_like(update[key])
                self.velocity[key] = torch.zeros_like(update[key])
        
        optimized = OrderedDict()
        
        for key, param in update.items():
            # 更新动量
            self.momentum[key] = self.beta1 * self.momentum[key] + (1 - self.beta1) * param
            
            # 更新速度
            self.velocity[key] = self.beta2 * self.velocity[key] + (1 - self.beta2) * (param ** 2)
            
            # 偏差修正
            momentum_corrected = self.momentum[key] / (1 - self.beta1 ** self.step_count)
            velocity_corrected = self.velocity[key] / (1 - self.beta2 ** self.step_count)
            
            # 应用更新
            optimized[key] = param - self.learning_rate * momentum_corrected / (torch.sqrt(velocity_corrected) + self.epsilon)
        
        return optimized

# 聚合器工厂函数
def create_advanced_aggregator(aggregator_type: str, **kwargs):
    """创建高级聚合器实例"""
    aggregator_map = {
        'flame': FLAMEAggregator,
        'fednova': FedNovaAggregator,
        'scaffold': SCAFFOLDAggregator,
        'trimmed_mean': TrimmedMeanAggregator,
        'fedopt': FedOptAggregator
    }
    
    if aggregator_type.lower() not in aggregator_map:
        raise ValueError(f"不支持的聚合器类型: {aggregator_type}")
    
    return aggregator_map[aggregator_type.lower()](**kwargs)
