{"model_name": "LeNet", "dataset": "MNIST", "aggregator": "martFL", "n_participant": 10, "n_adversary": 1, "attack": "free_rider", "global_epoch": 25, "local_epoch": 2, "learning_rate": 0.001, "batch_size": 32, "enable_pol": true, "pol_verification_threshold": 10.0, "pol_verification_budget": 1, "pol_save_strategy": "epoch", "pol_entropy_analysis": true, "pol_quality_aware": true, "device": "auto", "seed": 43, "run_id": 1, "experiment_id": "d1f5677638fe", "timestamp": 1754020418.945586}