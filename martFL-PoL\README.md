# martFL-PoL: 联邦学习攻击鲁棒性实验框架

## 🎯 项目简介

基于学习证明(PoL)的联邦学习攻击鲁棒性增强方案，测试martFL算法对各种攻击的防御效果。

## 🚀 快速开始

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行实验 (选择一个)
python experiment.py --quick      # 快速测试 (30分钟)
python experiment.py --standard   # 标准实验 (3小时)
python experiment.py --full       # 完整实验 (6天)

# 3. 查看结果
# 结果自动保存在 experiment_results/ 目录
```

**就这么简单！** 🎉

## 🔄 分多天运行 (推荐)

如果实验时间太长，可以分多天运行：

```bash
# 第1天：开始实验
python experiment.py --incremental --target_runs 3 --standard

# 第2天：继续第2轮 (运行相同命令)
python experiment.py --incremental --target_runs 3 --standard

# 查看进度
python experiment.py --show_progress
```

## 🎯 自定义实验

```bash
# 测试特定攻击和防御
python experiment.py --attacks free_rider backdoor --defenses martFL FLTrust

# PoL效果对比
python experiment.py --enable_pol both --attacks free_rider rescaling sybil

# 查看所有选项
python experiment.py --help
```

## 📈 实验结果

实验完成后，结果自动保存在 `experiment_results/` 目录，包含性能对比图表和详细分析报告。

## ❓ 常见问题

**Q: 实验需要多长时间？**
- `--quick`: 约30分钟
- `--standard`: 约3小时
- `--full`: 约6天 (建议用 `--incremental` 分多天运行)

**Q: 如何中断和恢复实验？**
使用 `--incremental` 模式，可以随时中断，下次运行相同命令继续。

**Q: 如何查看更多选项？**
运行 `python experiment.py --help` 查看所有可用选项。

---

**简单易用的联邦学习攻击鲁棒性实验框架！** 🎉
