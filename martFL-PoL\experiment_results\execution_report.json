{"summary": {"total_experiments": 2, "successful": 0, "failed": 2, "success_rate": 0.0, "total_execution_time": 4.979714393615723, "average_time_per_experiment": 2.4898571968078613}, "successful_experiments": [], "failed_experiments": [{"experiment_id": "c2fbae3d6e44", "error_message": "\nA module that was compiled using NumPy 1.x cannot be run in\nNumPy 2.2.6 as it may crash. To support both 1.x and 2.x\nversions of NumPy, modules must be compiled with NumPy 2.0.\nSome module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n\nIf you are a user of the module, the easiest solution will be to\ndowngrade to 'numpy<2' or try to upgrade the affected module.\nWe expect that some modules will need time to support NumPy 2.\n\nTraceback (most recent call last):  File \"/mnt/persist/workspace/martFL-PoL/src/main.py\", line 14, in <module>\n    from participant import *\n  File \"/mnt/persist/workspace/martFL-PoL/src/participant.py\", line 6, in <module>\n    from torchvision.datasets import MNIST,FashionMNIST,CIFAR10,SVHN,EMNIST\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/__init__.py\", line 5, in <module>\n    from torchvision import datasets, io, models, ops, transforms, utils\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/__init__.py\", line 17, in <module>\n    from . import detection, optical_flow, quantization, segmentation, video\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/__init__.py\", line 1, in <module>\n    from .faster_rcnn import *\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/faster_rcnn.py\", line 16, in <module>\n    from .anchor_utils import AnchorGenerator\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/anchor_utils.py\", line 10, in <module>\n    class AnchorGenerator(nn.Module):\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/anchor_utils.py\", line 63, in AnchorGenerator\n    device: torch.device = torch.device(\"cpu\"),\n/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/anchor_utils.py:63: UserWarning: Failed to initialize NumPy: _ARRAY_API not found (Triggered internally at ../torch/csrc/utils/tensor_numpy.cpp:77.)\n  device: torch.device = torch.device(\"cpu\"),\n/usr/local/lib/python3.10/dist-packages/scipy/__init__.py:146: UserWarning: A NumPy version >=1.16.5 and <1.23.0 is required for this version of SciPy (detected version 2.2.6\n  warnings.warn(f\"A NumPy version >={np_minversion} and <{np_maxversion}\"\nTraceback (most recent call last):\n  File \"/mnt/persist/workspace/martFL-PoL/src/main.py\", line 14, in <module>\n    from participant import *\n  File \"/mnt/persist/workspace/martFL-PoL/src/participant.py\", line 7, in <module>\n    from dataset import *\n  File \"/mnt/persist/workspace/martFL-PoL/src/dataset.py\", line 5, in <module>\n    from scipy.stats import powerlaw\n  File \"/usr/local/lib/python3.10/dist-packages/scipy/stats/__init__.py\", line 441, in <module>\n    from .stats import *\n  File \"/usr/local/lib/python3.10/dist-packages/scipy/stats/stats.py\", line 37, in <module>\n    from scipy.spatial.distance import cdist\n  File \"/usr/local/lib/python3.10/dist-packages/scipy/spatial/__init__.py\", line 96, in <module>\n    from .kdtree import *\n  File \"/usr/local/lib/python3.10/dist-packages/scipy/spatial/kdtree.py\", line 5, in <module>\n    from .ckdtree import cKDTree, cKDTreeNode\n  File \"ckdtree.pyx\", line 1, in init scipy.spatial.ckdtree\nValueError: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject\n", "return_code": 1}, {"experiment_id": "d1f5677638fe", "error_message": "\nA module that was compiled using NumPy 1.x cannot be run in\nNumPy 2.2.6 as it may crash. To support both 1.x and 2.x\nversions of NumPy, modules must be compiled with NumPy 2.0.\nSome module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n\nIf you are a user of the module, the easiest solution will be to\ndowngrade to 'numpy<2' or try to upgrade the affected module.\nWe expect that some modules will need time to support NumPy 2.\n\nTraceback (most recent call last):  File \"/mnt/persist/workspace/martFL-PoL/src/main.py\", line 14, in <module>\n    from participant import *\n  File \"/mnt/persist/workspace/martFL-PoL/src/participant.py\", line 6, in <module>\n    from torchvision.datasets import MNIST,FashionMNIST,CIFAR10,SVHN,EMNIST\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/__init__.py\", line 5, in <module>\n    from torchvision import datasets, io, models, ops, transforms, utils\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/__init__.py\", line 17, in <module>\n    from . import detection, optical_flow, quantization, segmentation, video\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/__init__.py\", line 1, in <module>\n    from .faster_rcnn import *\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/faster_rcnn.py\", line 16, in <module>\n    from .anchor_utils import AnchorGenerator\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/anchor_utils.py\", line 10, in <module>\n    class AnchorGenerator(nn.Module):\n  File \"/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/anchor_utils.py\", line 63, in AnchorGenerator\n    device: torch.device = torch.device(\"cpu\"),\n/usr/local/lib/python3.10/dist-packages/torchvision/models/detection/anchor_utils.py:63: UserWarning: Failed to initialize NumPy: _ARRAY_API not found (Triggered internally at ../torch/csrc/utils/tensor_numpy.cpp:77.)\n  device: torch.device = torch.device(\"cpu\"),\n/usr/local/lib/python3.10/dist-packages/scipy/__init__.py:146: UserWarning: A NumPy version >=1.16.5 and <1.23.0 is required for this version of SciPy (detected version 2.2.6\n  warnings.warn(f\"A NumPy version >={np_minversion} and <{np_maxversion}\"\nTraceback (most recent call last):\n  File \"/mnt/persist/workspace/martFL-PoL/src/main.py\", line 14, in <module>\n    from participant import *\n  File \"/mnt/persist/workspace/martFL-PoL/src/participant.py\", line 7, in <module>\n    from dataset import *\n  File \"/mnt/persist/workspace/martFL-PoL/src/dataset.py\", line 5, in <module>\n    from scipy.stats import powerlaw\n  File \"/usr/local/lib/python3.10/dist-packages/scipy/stats/__init__.py\", line 441, in <module>\n    from .stats import *\n  File \"/usr/local/lib/python3.10/dist-packages/scipy/stats/stats.py\", line 37, in <module>\n    from scipy.spatial.distance import cdist\n  File \"/usr/local/lib/python3.10/dist-packages/scipy/spatial/__init__.py\", line 96, in <module>\n    from .kdtree import *\n  File \"/usr/local/lib/python3.10/dist-packages/scipy/spatial/kdtree.py\", line 5, in <module>\n    from .ckdtree import cKDTree, cKDTreeNode\n  File \"ckdtree.pyx\", line 1, in init scipy.spatial.ckdtree\nValueError: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject\n", "return_code": 1}], "device_usage": {"cpu": {"total_experiments": 2, "successful_experiments": 0, "total_time": 4.974416494369507, "average_time": 2.4872082471847534}}, "timestamp": 1754020423.9434285}