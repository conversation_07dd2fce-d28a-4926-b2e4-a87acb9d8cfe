
--- 第 1 页 ---
In 42nd IEEE Symposium on Security and Privacy 1
Proof-of-Learning: Definitions and Practice
He<PERSON><PERSON>i <PERSON>*§, <PERSON>*§, <PERSON>§, <PERSON>+§, <PERSON><PERSON><PERSON>§,
<PERSON><PERSON><PERSON>†, <PERSON>§
University of Toronto and Vector Institute§, University of Wisconsin-Madison†
Abstract—Training machine learning (ML) models typically Inourwork,wedesignastrategythatwillallowaparty–the
involves expensive iterative optimization. Once the model’s final prover–to generate a proof that will allow another party–the
parametersarereleased,thereiscurrentlynomechanismforthe
verifier–toverifythecorrectnessofthecomputationperformed
entity which trained the model to prove that these parameters
duringtraining.InthecaseofML,thistranslatestotheprover were indeed the result of this optimization procedure. Such a
mechanismwouldsupportsecurityofMLapplicationsinseveral generating a proof to support its claims that it has performed
ways. For instance, it would simplify ownership resolution when a specific set of computations required to obtain a set of
multiple parties contest ownership of a specific model. It would model parameters. In the model stealing scenario, the prover
also facilitate the distributed training across untrusted workers would be the model owner, and the verifier would be a legal
where Byzantine workers might otherwise mount a denial-of-
entityresolvingownershipdisputes.Inthedistributedlearning
service by returning incorrect model updates.
In this paper, we remediate this problem by introducing scenario,theproverwouldbeoneoftheworkers,andthever-
the concept of proof-of-learning in ML. Inspired by research ifierthemodelowner.Wenameourstrategyproof-of-learning
on both proof-of-work and verified computations, we observe (PoL).Unlikeprioreffortsrelatedtoproofs-of-work[12],[13],
how a seminal training algorithm, stochastic gradient descent, our approach is not aimed at making computation expensive
accumulates secret information due to its stochasticity. This
so as to inhibit denial-of-service attacks.
produces a natural construction for a proof-of-learning which
demonstrates that a party has expended the compute require to When developing our concept for PoL, we consider only
obtain a set of model parameters correctly. In particular, our the training phase and not the inference phase; the cost of
analyses and experiments show that an adversary seeking to inference is generally much lower, and there already exist
illegitimately manufacture a proof-of-learning needs to perform
mechanismstoensuretheintegrityofMLinferenceperformed
at least as much work than is needed for gradient descent itself. byanotherparty[14].Inourdesign,wewishtodesignaproof
We also instantiate a concrete proof-of-learning mechanism
in both of the scenarios described above. In model ownership
strategythataddslimitedoverheadtothealreadycomputation-
resolution,itprotectstheintellectualpropertyofmodelsreleased ally intensive process of training. Deep models do not have
publicly. In distributed training, it preserves availability of the closed form solutions, and use variants of gradient descent
training procedure. Our empirical evaluation validates that our
as the de-facto choice for training. Additionally, stochastic
proof-of-learning mechanism is robust to variance induced by
gradient-based optimization methods used in deep learning,
the hardware (e.g., ML accelerators) and software stacks.
like stochastic gradient descent (SGD), update model param-
eters iteratively over long sequences by computing unbiased
estimates of the true gradient [15]. Naturally, this sequence
I. INTRODUCTION represents the work performed by the prover in training their
model. We propose that PoL for ML should demonstrate two
Training machine learning (ML) models is computationally
properties:(a)theproverperformedthenecessaryoptimization
and memory intensive [1], often requiring hardware accelera-
(expending computational resources) to train an ML model,
tion. GPUs [2], TPUs [3], and FPGAs [4] are used to ensure
and(b)thesestepswerecomputedcorrectly,i.e.,thatwehave
efficient training. In the status quo, there is no way for an
integrity of computation.
entity to prove that they have performed the work required
There has been extensive research in proof systems related
to train a model. This would be of immense utility in at
to other applications. Verified computations relates to settings
least two settings. First, once a model is released publicly
whereoutcomesofoutsourcedcomputation(suchasinclient-
intentionally or unintentionally (i.e., it is stolen), the model’s
server architectures) can be verified [16]–[20]. Theoretical
ownermaybeinterestedinprovingthattheytrainedthemodel
advances and efficient hardware design have enabled both
as a means to resolve and claim ownership—for instance,
smaller proofs and more efficient verification strategies [21],
resolving claims related to model stealing attacks [5]–[9].
[22]. The simplest scheme, however, involves duplicated exe-
Second,amodelownermayseektodistributethetraining[10]
cution i.e., re-executing the computation performed to verify
acrossmultipleworkers(e.g.,virtualmachinesinacloud)and
the validity of the proof.
requires guarantees of integrity of the computation performed
Followingthisintuition,weintroduceinageneralapproach
by these workers. This would defend against some of the
to obtain a PoL which enables verifying the computation
parties being corrupted accidentally (e.g., due to hardware
performed during training (see § V). We then instantiate a
failure) or maliciously (e.g., by an adversary which relies on
concrete PoL which utilizes the difficulty to invert gradient
Byzantine workers to perform denial-of-service attacks [11]).
descent(see§V).Theaddedadvantagehereisthatoperations
*Jointleadauthors;+jointsecondaryauthors. involvinggradientdescentarecomputedaspartofthelearning
1202
raM
9
]GL.sc[
1v33650.3012:viXra

--- 第 2 页 ---
2
procedure,andcanbeusedforgeneratingtheproofaswell.In functionsaswell,inwhichPoWfunctionsforcetheadversary
our work, the guarantees sought by the prover are analogous toexpendsignificantcomputationalresources,whetherCPUor
to those in the verifiable computations literature: given (i) a memoryresources,inordertorequestaccesstotheservice.We
(random) distribution to draw the initial model weights from, revisitthismotivationin§IV,butwiththeperspectiveofML
(ii) the model’s final weights, and (iii) a dataset, the prover systemsinmind.ThetermPoWitselfwaslaterintroducedby
must provide a sequence of batch indices and intermediate JakobssonandJuels[13].Akeypropertyofthisformulationis
model updates that, starting from the initialization, one can that PoW relies largely on the existence of one-way functions
replicate the path to the final model weights. This allows a popular in cryptography to establish an asymmetry between
verifier to recompute any of the steps of gradient descent of the party doing the computation and the party verifying that
theirchoosingtoconfirmthevalidityofthesequenceprovided. the computation was performed.
Thisinturndemonstratesthattheproverhasindeedperformed Instandardtwo-roundPoWprotocols,theproverreceivesa
the computation required to obtain the final parameter values. query including a cryptographic puzzle, frequently involving
However, verification also requires the expensive process of or indirectly based on a hashed randomly generated value
gradientcomputationtoverifythestepstaken,asourproposal or structure computed by the verifier. The prover solves the
is based on re-execution. To make verification more computa- computational puzzle and returns the value, which the verifier
tionally affordable, we introduce a heuristic for the verifier to eitheracceptsasasolutiontotheproblemorrejects.Generally,
select only a subset of the pairs of model parameter states to theprocessofsolvingthecomputationalproblembytheprover
verify. This allows the verifier to trade-off the confidence of depends, directly or indirectly, on computation of a pre-image
verification with its cost: if the verifier randomly picks a set of a hashed random number generated and computed by the
of parameter pairs, then with sufficiently many choices, it can verifier, a known hard and expensive problem.
be confident of the proof’s validity. Dwork and Naor [12] enumerated several PoW strategies
Therearemanysequencesthatcanbeobtainedfromagiven predicatedonintegersquarerootmodulelargeprimeproblem:
start state to a given final state (owing to the various sources e.g., the Fiat Shamir signature scheme and the Ong-Schnorr-
of stochasticity involved in training). However, through our Shamir signature scheme. Since then, many methods have
theoretical analysis in § VII, we observe that obtaining these beenproposedforPoWfunctions.TheseinitialPoWfunctions
states through conventional training (i.e., moving forward constituted CPU-bound functions and later memory-bound
throughthesequence)ismoreefficientthaninvertinggradient PoWfunctionsgraduallygrewoutofthefieldaswell.Among
descent (i.e., moving backwards through the sequence). Our PoW functions are partial hash inversion [13], moderately
analysis shows that inverting gradient descent takes at least hard memory-bound functions [23], guided tour puzzle [24],
as much work as training. Thus, it is hard for an adversary to Diffie-Helman problem-based [25], Merkle-tree-based [26],
spoof a PoL using such a strategy. Hokkaido [27] and Cuckoo cycle [28].
In summary, our contributions are the following: Recently, systems that incorporate PoW have also been
motivated by or used for various cryptocurrencies. Many
• In § IV, we formalize the desiderata for a concept of
current cryptocurrencies, such as Bitcoin and HashCash [29],
proof-of-learning, the threat model we operate in, and
[30], employ systems based on PoW algorithms. Blockchain
introduce a formal protocol between the different actors
systemsincryptocurrencyutilizeamodifiedsetupofthetypi-
involved in generating a PoL.
cal setting and actors in PoW frameworks for DoS attacks. In
• In§V,weintroduceageneralmechanismforPoLbased
Bitcoin, miners competitively attempt to secure a payment as
ontheobservationthatstochasticgradientdescentutilized
follows.First,theycollectunverifiedBitcointransactionsfrom
during training is difficult to invert.
coin dealers in the Bitcoin network. Second, they combine
• We analytically prove the correctness of our mechanism
thesetransactionswithotherdatatoformablockwhichisonly
in § VI, and then verify experimentally that it can be
accepted once the miner has found a nonce number hashing
implementeddespitehardwareandsoftwarestochasticity.
to a number in the block with enough leading zeros.
• We analyze the security of our proposed mechanism in
§ VII through an analysis of entropy growth in gradient
B. Security in ML Systems
descent, and evaluate possible spoofing strategies an
adversary may rely on to pass verification. Most work on security in the context of ML [31]–[33] has
• Our code is open-sourced at github.com/cleverhans-lab/ focused on the integrity of model predictions [34]–[36] or on
Proof-of-Learning. providing guarantees of privacy to the training data [37]. Our
effortsondevelopingaproof-of-learning(orPoL)conceptfor
thetrainingalgorithmareinstead,asillustratedbybothofthe
II. RELATEDWORK
use cases discussed in § I, most relevant to two previous lines
A. Proof-of-Work in Cryptography
of work: the first is model stealing, the second is Byzantine-
The concept of proof-of-work (or PoW), where one party tolerant distributed learning.
provestoanotherthatithasexpendedcomputationalresources a) Model Ownership & Extraction: The intellectual
towards a computation result, was first introduced by Dwork property of model owners can be infringed upon by an
and Naor [12]. The concept was motivated as a defense from adversary using model extraction attacks [5]. Most extraction
denial-of-service (DoS) attacks against email and network attacks targeting DNNs are learning-based: the adversary col-
providers. This was the main motivation for many later PoW lects a substitute dataset (i.e., consists of data from a similar

--- 第 3 页 ---
3
distribution or synthetic data), queries the victim model to 3) Random Initialization: Before training, each weight vec-
obtain labels, and then re-trains a surrogate model that is tor w ∈ W requires an initial value. These values are
i
functionally similar to the victim [5]–[9]. Attacks may also oftenrandomlyassignedbysamplingfromadistribution.
use side-channel information [38]. Values are sampled from a zero-centered uniform or
Therearecurrentlytwotypesofdefensesagainstextraction Gaussiandistributionwhosestandarddeviationisparam-
attacks: (a) restricting information released for each query eterized by either the number of neurons in the input
answered by the model [5], [39], and (b) assessing if a layer, the output layer, or both [57]–[59].
suspectedmodelisindeedastolencopyofavictimmodel.The The final set of parameters are learned by training the ML
lattercanbedoneintwoways.Ifthemodelwaswatermarked, model using empirical risk minimization [60]. A training
one can query for the corresponding triggers [40]–[42]. If dataset is sampled from the data distribution D ∼ D. The
tr
that is not the case, one can use the training data directly to expected risk of a model on this dataset is then quantified
perform dataset inference [43]. However, decreasing extrac- using a loss: a real valued function L(f (x),y) that is the
W
tion efficiency by restricting information returned by queries objectiveforminimization.Thelosscharacterizesthediscrep-
comes at the expense of the model’s utility [5], [44]–[46]. ancy between the model’s prediction f (x) and the ground
W
Similarly, watermarking trades utility with the robustness of truth y. A common example is the cross-entropy loss [61].
thewatermarkwhileadditionallyrequiringthemodificationof Training occurs in an iterative manner by continuously
thetrainingprocess[40],[47]–[50].Thus,watermarksmaybe sampling a (mini)batch of training data, without replacement,
removedfromadeployedmodelormadeineffective[51]–[53]. from D ; each such iteration is called a step1. For each step,
tr
In contrast, our work does not impact training and produces stochastic gradient descent [62] updates the model’s parame-
a PoL which is immutable (see § IV), we also do not restrict ters to minimize the empirical risk by taking the gradient of
information released at inference time. the loss with respect to the parameters. Thus, at each step
i∈[T], we obtain a new set of weights W as follows:
i
C. Byzantine-tolerant distributed ML
W =W −η·∇ ,Lˆ (1)
In the second scenario we described in § I, we consider
i i−1 Wi−1 i−1
a setting where a model owner wishes to distribute the where η is the learning rate hyperparameter, and Lˆ =
i−1
compute required to train a model across a pool of potentially 1 (cid:80) L(f (x),y) denotes the average loss com-
m (x,y)∼Db Wi−1
untrusted workers [54]. Each of these workers receives a few puted over a random batch D ⊆D of size m. An epoch is
b tr
batchesoftrainingdata,performssomegradientdescentsteps, onefullpassthroughD whichcontainsS steps.Thetraining
tr
and then regularly synchronizes parameters with the model process overall has a total of E epochs. Thus, assuming the
owner. In this distributed setting, we note that prior work has size m of a batch is fixed during training, training the model
studiedtrainingalgorithmswhicharerobusttothepresenceof requires a total of T =E·S steps.
Byzantine [55] workers: such workers may behave arbitrarily
and return corrupted model updates to the model owner [11]. IV. FORMALIZINGPOL
As we will introduce in § IV, verifiable PoL forms a defense
We wish to show that one can verify the integrity of the
against DoS attacks in this context. In addition, our PoL may
training procedure used to obtain an ML model. This in turn
be used to provide integrity guarantees by confirming the
can also be used to show proof of ownership. We focus on
correctness of computations performed by the workers.
training because it induces the largest computational costs.
We note that there is prior work in verifiable computing
III. BACKGROUNDONMACHINELEARNING
investigating inference-time computation but that these were
Throughout our work, we define [n] := {1,...,n}. Con- notdesignedfortrainingalgorithmsandrequiremodifyingthe
sideradatadistributionDoftheformX×Y,suchthatX isthe algorithmtoaccommodatecryptographicprimitivessuchasan
space of inputs, and Y is the space of outputs. An ML model interactive proof system [63]–[65]. Instead, we formulate our
is a parameterized function of the form f W : X → Y, where approach such that no changes need to be made to the model
W denotes the model parameters. For the purposes of this architectureandtrainingalgorithmbeyondadditionallogging.
work, we assume that these models are deep learning models Thisenablesaseamlessintegrationformodelownerstocreate
which requires additional terminology. PoLandmakeclaimsofhavingtrainedamodel.Ourapproach
1) Model Architecture: A deep neural network is a function for PoL is naturally extended to two scenarios:
comprised of many layers, each performing a linear- 1) A party can claim ownership of a trained model f .
transformation on their input with optional non-linear 2) An entity outsources computation to some client W (a T s in
activations [56]. The structure of these layers, e.g., the distributed learning), then the results returned by the
number of neurons and weights, the number of layers l, client (i.e., fc ) can be trusted.2
and activations is termed the model architecture.
WT
The party performing the computation is referred to as the
2) Model Weights: The parameters of the deep learning
prover T. To verify the integrity of its computation (either
modelarecommonlycalleditsweights.Eachlayeri∈[l]
for ownership resolution or in the outsourced computation
is comprised of learnable weights denoted w , including
i
the additional bias term. Collectively, we denote the set 1Onestepcorrespondstoprocessingonebatchofdata.
of per-layer weights {w
1
,··· ,w
l
} as W. 2Thesuperscriptcdenotesacomputationexecutedlocallyataclient.

--- 第 4 页 ---
4
scenario), T generates a certificate, henceforth referred to as by (a) drawing on some source of randomness, or (b) using
the Proof-of-Learning (or PoL) performed to obtain f .3 some other parameters (with a valid PoL) for initialization of
WT
We denote such a PoL as P(T,f ). When the integrity of its model parameters (W ); we will more formally define the
WT 0
the computation (ergo model ownership) is under question, latterin§V-D.T thentrainstheirMLmodelandobtainsfinal
an honest and trusted verifier V analyzes P(T,f ) and parametersW .Throughtraining,T accumulatessomesecret
WT T
determines its validity (i.e., a valid PoL implies that T information associated with training; this information is used
performedthecomputationrequiredtoobtainf ).Formally, to construct P(T,f ) which can be used to prove integrity
WT WT
avalidPoLisonewhereeachcomponentiswell-formed(refer of the computation performed by T to obtain W from W .
T 0
§ IV-C), and V can reconstruct the PoL in its entirety. An To validate the integrity of the computation, V may query
adversary A is one who wishes to subvert this process. T for the PoL and T returns a subset (or all of) the secret
information obtained during training. Using this knowledge,
A. Threat Model V should be able to ascertain if the PoL is valid or not.
Dishonest spoofing is any strategy that requires lesser Desired Guarantees. A cannot (a) easily reconstruct the
computational expenditure than that made by the prover in secretinformationassociatedwithP(T,f )(neededforthe
WT
generatingtheproof;weformallydefinethistermin§VII-A. retraining-based spoofing strategy), or (b) efficiently recon-
Theprimaryscenariowewishtomitigateagainstistheability structanothervalidPoLP(A,f )orP(A,f)forf ≈f .
WT WT
of an adversary A to efficiently spoof P(T,f WT ), i.e., we In particular, the computational resources needed should (ide-
want to verify computation to train the model on the part of ally)bethesameormoreasthecostofvalidproofgeneration.
the prover. By spoofing, A can claim to have performed the We formalize the computational requirements below:
computation required (to produce f WT , for example). Since Property 1. LetC T denotearandomvariablerepresentingthe
A has not expended (significant) computational resources nor cost (both computation and storage) associated with T
t t r o ai h n a e v d e th P e ( m T o , d f e W l T to ). b T e h a u b s l , e A to t p r r i o es du t c o e c f r W ea T te ,t P he ( y A a , r f e W u T n ) lik th el a y t t t r h a e in v i e n r g ifi f c W at T io . n Le p t ro C c V ed d u e re n . ot W e e th t e hu c s os r t e r q a u n i d re om tha v t ariable of
passes verification, even if that PoL is not valid. We consider
the following scenarios for spoofing: E[C V ]≤E[C T ]
(a) Retraining-based Spoofing: A aims to create the exact
Property 2. Let C be the cost random variable associated
A
same PoL for f as T i.e., P(A,f )=P(T,f ).
WT WT WT withanyspoofingstrategyattemptedbyanyAasidefrom
(b) Stochastic Spoofing: A aims to create a valid PoL for
the honest strategy (i.e. training f ). We require that
f , but this may not be the same as T’s PoL i.e.,
WT
P W (A T ,f )(cid:54)=P(T,f ). E[C ]≤E[C ]
WT WT T A
(c) StructurallyCorrectSpoofing:Aaimstocreateaninvalid
Note here that the second property should hold no matter
PoLforf butsuchaPoLpassesverificationforf .
WT WT which of the four scenarios from §IV-A we consider: in
(d) Distillation-basedSpoofing:AaimstocreateavalidPoL
particular the cost of the adversary should be higher even if
using a modified version of f (say f) i.e., P(A,f)(cid:54)=
WT they choose scenario (c) and form a structurally correct PoL
P(T,f ). Note that the adversarial approximation of
WT which is invalid but still passes verification.
themodelf(≈f )hasthesametest-timeperformance.
WT
In our security analysis (see § VII), we comment on the
efficiency of the above spoofing strategies; for the adversary, C. Defining PoL
it is desirable that the aforementioned are dishonest spoofing Definition 1 (PoL). For a prover T, a valid PoL is defined
strategies. We assume the following adversarial capabilities: as P(T,f ) = (W,I,H,A) where all the elements of the
WT
1) A has full knowledge of the model architecture and tuple are ordered sets indexed by the training step t∈[T]. In
parameters (i.e., weights). In addition, A has access to particular, (a) W is a set of model specific information that
the loss function, optimizer, and other hyperparameters. is obtained during training, (b) I denotes information about
2) A has full access to the training dataset, and can modify the specific data points used to obtain each state in W, (c)
it. Note that the objective of A is not to infer sensitive H represents signatures of these training data points, and (d)
information from the dataset, but use it to spoof a PoL. A that incorporates auxiliary information that may or may
3) A does not have access to the various sources of ran- not be available to A, such as hyperparameters M, model
domness used by T. These sources include randomness architecture, optimizer and loss choices.
associated with batching, parameter initialization, chosen
The information in Definition 1 encapsulates all the infor-
random seeds, and other intrinsic sources of randomness
mation required to recreate (and consequently verify) a PoL.
such as hardware accelerators [66].
T publishes some deterministic variant of W (e.g., encrypted
W). Our scheme should ensure that recreating the states in
B. Protocol Overview
W without knowledge of I,H and some designated subset of
We define PoL in ML as a n ≥ 1-round protocol between elements in A is hard; this should dissuade any adversary in
the prover T and verifier V. The protocol is initiated by T
recreating the prover T’s PoL. In addition to this, we should
3The case with fc is similar. For generality, we proceed to define our also ensure recreating W T without W is hard so that the
workwithreference W to T fWT . adversary cannot spoof (refer §VII) the PoL with a different

--- 第 5 页 ---
5
PoL P(A,f ) (cid:54)= P(T,f ). To this end, we require that Alternatively, in a lazy verification scenario (§ V-D1) , T can
WT WT
algorithms included within A be from a known accepted list delay the release of the exact data points to V until they are
of algorithms, or have their own PoL (refer § V-D3). More explicitly queried. In such a case, T is necessitated to include
concretely, any PoL (and the strategy to generate the PoL) a signature (represented using function h(.)) of the training
should satisfy the following properties: data as part of the PoL. We require this as an abundance of
G1. Correctness: A PoL for f should be verifiable with precautionsothatanadversarialproverattemptingstructurally
WT
high probability if the prover T obtained this PoL by correct spoofing (see § IV-A) cannot release a structurally
trainingamodelfromarandominitializationofthemodel correct yet invalid PoL and then later attempt to synthesize
parameters and until their convergence to f . a dataset which would make this PoL valid.
G2. Security: If A is able to dishonestly spoof t
W
he
T
PoL, then
TheprocessofobtainingupdatesinWissimilartotraining
it will be detected with high probability.
whenaidedbytheinformationcontainedinI,HandA.Inour
G3. Verification Efficiency: Verifying the correctness of a protocol we only retain hyperparameters M as our auxiliary
proof should ideally be computationally less expen- information in A. Thus, by querying this information, V can
sive than generating the proof. Additionally, verification recreate the updates in a specific subset by re-executing the
shouldsucceedeveniftheverifierusesdifferenthardware computation. By doing so, V is able to attest the computation
than the prover. performed by T. We detail this verification in § V-C.
G4. Model Agnostic: A proof generation strategy should be
general i.e., should be applicable to models of varying B. PoL Creation
nature and complexity.
G5. Limited Overhead: Generating the proof should induce Algorithm 1 PoL Creation
limited overhead to the already computationally expen- Require: Dataset D, Training metadata M
sive training procedure. Require: V’s public key Kpub
V
G6. ConciseProof:Theproofgeneratedshouldbesmallwith Require: E,S,k (cid:46) Numberofepochs,stepsperepoch,checkpointing
respect to the number of steps of training (and ideally of interval
constant size). Optional: W 0 ,ζ (cid:46) Initializationweightandstrategy
1:
W←{},I←{},H←{},M←{}
V. APOLMECHANISMBASEDONGRADIENTDESCENT 2: if W 0 =∅ then
Our proposal for generating a PoL is based on gradient 3: M 0 ←ζ
descent. At the core, our mechanism relies on the difficulty 4: W 0 ←init(ζ)
to invert gradient descent. In this section, we simplify the 5: for e←0,...,E−1 do (cid:46) Trainingepochs
notation for brevity i.e., P(T,f WT ) is now P(f WT ). 6: I ←getBatches(D,S)
7: for s←0,...,S−1 do (cid:46) stepsperepoch
A. Mechanism Overview 8: t=e·S+s
In our proposed mechanism, T reveals to V some of 9: W t+1 ←update(W t ,D[I s ],M t )
10: I.append(I t )
the intermediate weights achieved during training as its PoL
P(f ). More specifically, T releases: (a) the values of 11: H.append(h(D[I t ]))
WT 12: M.append(M t )
the weights (or model updates) at periodic intervals during
13: if tmodk =0 then
training, and (b) the corresponding indices of the data points
14: W.append(W t )
from the training set which were used to compute said model
15: else
updates. To ensure that A cannot copy the PoL as is, we
16:
W.append(nil)
require that T encrypt their PoL P(f ) with V’s public
key Kpub to obtain R:=enc(P(f ),
W
K
T
pub), and then sign 17:
A←{M}
it with
V
T’s own private key before
WT
publis
V
hing the PoL. The 18:
R←enc((W,I,H,A),K
V
pub)
(cid:16) (cid:17)
proof (or its signature) can be timestamped, or published in 19: return R,h R,Kpriv
a public ledger. This ensures that verifying its validity is as T
simple as a lookup operation. This prevents replay attacks,
where A would claim to have published the PoL first. In Algorithm 1, we present the concrete mechanism to
To commence verification, V first verifies the authenticity create PoL P(f ). W is a flattened list of all recorded
WT
of the signature using T’s public key and proceeds to decrypt weights across all epochs indexed by the proof step t. The
the encrypted PoL using its private key Kpriv. It then verifies mappingfromtrainingstepstotheproofsteptist=e·S+s,
V
the provenance of the initial weights W . These are either where S is the number of training steps per epoch and e
0
(a) sampled from the claimed initialization distribution, or (b) is the epoch counter (of a total of E epochs). We only
come from a valid external source, i.e., have their own PoL. append a weight W every kth step of the training, and
t
See§V-D4and§V-D3,respectively.Next,V queriesT forthe otherwise add ⊥ at that index. Observe that checkpointing
datapointsrequiredtocomputeaspecific subsetofupdatesin is commonly performed as part of training and adds limited
W. There are two possibilities. Either the dataset is released overhead (G5). k is a parameter which we call checkpointing
by T along with the PoL and is available to V immediately. interval; 1 is then the checkpointing frequency. Increasing
k

--- 第 6 页 ---
6
k helps optimize storage costs (refer § V-D2). T may use that for an honest T who has obtained all intermediate model
additional hyperparameters and optimizer specifications (e.g., weights, the particular choice of k is immaterial. Also since
learning rate schedules, etc.), which we denote as metadata δ is chosen to account for hardware and software tolerances,
M (to be included in M). To make sure that weights in W Algorithm 2 will correctly verify such an honest proof (G1).
t
will be verified on the same data samples f was originally Why only verify the largest updates? We verify the largest
WT
trained on, we require that P(f ) include a signature of the model updates because valid updates tend to have small mag-
WT
training data, i.e., h(D[I ]) in H along with the data indices nitude (to avoid overshooting during gradient descent), and
t
which are themselves included in I. we want to save computational cost of V. More importantly,
In Algorithm 1, init() is a method that initializes the any estimation error introduced by an adversary A wishing
weightsaccordingtoaninitializationstrategyζ beforetraining to recreate a proof at a smaller computational cost would be
commencement. In scenarios where the initial model state is easier to detect for these large model updates. This may be
obtained from elsewhere, we require that a PoL be provided becausetheadversarytriedtospoofavalidPoLbyfine-tuning
for the initial model state itself as well (see § V-D3). In models at large learning rates for few epochs, or because they
a similar vein, getBatches() randomly assigns a set of attempt to spoof a PoL with significant discontinuity to arrive
data indices (over the entire dataset) to each batch. Thus, the at a new W(cid:99) (see § VII-C1), We assume that the verifier V
output of the method is a list of T sets of indices. Finally, can verify at most Q·E largest updates (i.e., Q per epoch),
the method update() performs an update to the model which we denote as V’s verification budget. Similar to the
parameters using a suitable optimizer implementing one of slack parameter δ, Q is also a verification hyper-parameter
the variants of gradient descent as in Equation (1). which should be calibrated, and can be mandated by law.
Storage Cost. Theproofsizeis ES|W|where|W|indicates TimeComplexity. ThecomplexityofverificationisO(E·Q·
k
the size of a set of model weights i.e., a single checkpoint k·C )whereC isthetime-complexityofoneupdatestep
|W| |W|
(G6).Wenotethatiftheproverwouldliketodelaytheverifi- of the training loop with parameter size |W| (G3). Figure 7
cationuntilrequested(see§V-D1)thentheyshouldmaintaina in Appendix E shows a visualization of the bound.
copyofthedataset,whichadds|D|tothestoragecost,where Verification Success. We define the verification success rate
|D| is the size of the dataset. Increasing the checkpointing (VSR) of verifier V on a PoL P as:
interval linearly decreases the storage cost, however this can
come at the cost of verification accuracy (see §V-C). Storage
VSR(V,P(f
WT
)):=Pr[VERIFY[P(f
WT
)]=Success], (2)
costs are discussed in detail in § VI-C4. where P(f
WT
) = P(T,f
WT
) = (W,I,H,A) and VERIFY
is a simplified notation for the same function in Algorithm
2. Verification success can be described as the probability
C. PoL Verification
that the verifier accepts a PoL. Note that by nature of the
Algorithm 2 summarizes the verification algorithm. Every verification Algorithm 2, the probability of acceptance by the
PoL starts from a set of weights either sampled from the verifier depends on the probability of:
claimed initialization distribution, or from previously trained
1) W(cid:48) (i.e., calculated update from W by the verifier)
model weights. In the latter case, the prover needs to pro- t+k t
achieved within a δ-ball of the purported weights W .
vide a valid PoL for the pre-trained model weights, i.e., P0 t+k
2) V obtaining W(cid:48) from initial weights W in k steps.
(referenced in encrypted form in Algorithm 2 as R0). In the t+k t
Here, t = idx[q −1], denotes the step with the qth largest
case of sampling from the claimed initialization distribution,
k-step update in the given epoch e. As the update for each t
a statistical test is conducted to verify the claim. We discuss
in the verification procedure is calculated separately and the
these requirements and their importance in more detail in
value of W is directly obtained, these events for different
§ V-D3 and § V-D4, respectively. After this initial verification t
values of t are independent. To ease the notation assume that
step, we store the distance between each consecutive pair of
weights captured in W in a new list mag using d which is a I is reindexed so that j is the index corresponding to the qth
1
largest update. We re-write Equation (2) with ‘Success’ as 1,
distancemeasureinametricspace(suchasthep-norm).Once
and φ=(I,H,M),
every epoch, we sort mag to find the largest model updates
which we verify using the VERIFYEPOCH procedure. To
Pr[VERIFY[W,φ]=1]=
verify,V loadsuptheindexcorrespondingtothelargestmodel
update into its own model W(cid:48). Next, V performs a series of k (cid:89) E (cid:89) Q
t Pr[Tr ∧ dist ≤δ |φ]
updates to arrive at W(cid:48) which is compared to the purported e,q,k e,q+k
t+k
W in the PoL. We tolerate d (W(cid:48) ,W ) ≤ δ, where e=1q=1
t+k 2 t+k t+k E Q
d 2 is a distance measure (possibly different from d 1 ). δ is a = (cid:89)(cid:89) Pr[dist ≤δ |φ]·Pr[Tr |φ],
slack parameter that should be calibrated before verification e,q+k e,q,k
e=1q=1
starts, as it depends on hardware, model architecture, dataset,
checkpointing interval, and the learning hyperparameters. Al- where (a) dist = d (W(cid:48) ,W ) denotes the
e,(q)+k 2 e,(q)+k e,(q)+k
ternatively, acceptable ranges for δ may be mandated by law distancemeasurement,and(b)Tr :=W ⇒W(cid:48)
e,(q),i e,(q) e,(q)+i
andenforcedbytheverifier.Sincethepurposeofδ istoupper indicates the updates calculated by V of the i ≤ k steps in
bound the randomness in training, one heuristic is to set δ as the qth largest k-step update in epoch e and has achieved
theaverageofafewgradientupdatesduringtraining.Wenote W(cid:48) .Wealsohaveusedthefactthatthedistancebetween
e,(q)+i

--- 第 7 页 ---
7
Algorithm 2 Verifying a PoL terms in the product, therefore, if there is any uncertainty
1: function VERIFY(R,R0,Kpriv,f,D,Q,δ) (cid:46) encrypted regarding intermediate updates, their effect is compounded,
V
which in turn makes for a more stringent verification process.
PoLs,V’sprivatekey,model,dataset,querybudget,slackparameter
2:
W,I,H,M←dec(R,Kpriv) This comes at a trade-off with storage cost (see §V-B).
V
3: if R0 =∅ then
4: if VERIFYINITIALIZATION(W 0 )= FAIL then D. Practical Considerations
5: return FAIL Here, we discuss practical considerations to be made when
6: else if VERIFYINITPROOF(R0)= FAIL then implementing the mechanism we described so far.
7: return FAIL 1) Private Datasets & Lazy Verification: So far we have
8: e←0 (cid:46) Epochcounter assumed that the dataset used to train a model is public,
9: mag ←{} (cid:46) Listofmodelupdatemagnitudes so that V can use batch indices I in P(f ) to verify
WT
10: for t←0,...,T −1 do (cid:46) trainingstep model updates. It is also possible to use our PoL scheme for
11: if tmodk =0∧t(cid:54)=0 then private datasets. To do so, in addition to P(f ), T needs
12: mag.append(d 1 (W t −W t−k )) to publish a signature of their datapoints h(D W [ T I t ]),I t ∈ I
13: e t =(cid:98) S t(cid:99) (cid:46) Recoveringtheepochnumber but not the dataset. In this setup, the verification can be
14: if e t =e+1 then delayed until necessary (i.e., lazy verification), at which time
15: (cid:46) Newepochstarted.Verifythelastepoch T shouldrevealD[I t ]toV whoadditionallyhastoverifytheir
16: idx←sortedIndices(mag,↓) signatures with the published record.
17: (cid:46) getindicesfordecreasingorderofmagnitude 2) Amount of Data Needed: With lazy verification, the
18: if VERIFYEPOCH(idx) = FAIL then expected amount of data required to be transferred to the
19: return FAIL verifier V can be expressed as a function of S, E, k, and Q
20: e←e t ,mag ←{} (for simplicity here we assume Q is the same for all epochs).
Let c denotes the Binomial random variable representing the
21: return Success i
number of times data points i is sampled by VERIFYEPOCH
22: function VERIFYEPOCH(idx)
in Algorithm 2 (thus there are E trials). We assume each data
23: for q ←1,...,Q do
point is equally likely to be chosen such that in a certain trial
24: t=idx[q−1] (cid:46) indexofq’thlargestupdate ∀i ∈ [|D|],Pr(c = 1) = Q·k. Therefore, the probability of a
25: H t ←H t ,I t ←I t data point being i chosen at S least once is
26: VERIFYDATASIGNATURE(H t ,D[I t ]) Q·k
27: W t (cid:48) ←W t Pr(c i (cid:62)1)=1−Pr(c i =0)=1−(1− S )E (4)
28: for i←0,...,k−1 do
This means for dataset D, the expected amount of data for
29: I t+i ←I t+i ,M t+i ←M t+i Algorithm 2 is |D|[1−(1− Q·k)E].
30: W t (cid:48) +i+1 ←update(W t (cid:48) +i ,D[I t+i ],M t+i ) 3) InitialStateProvenancea S ndChainofTrust: Toimprove
31: W t+k ←W t+k convergence behavior and achieve better performance, most
32: if d 2 (W t (cid:48) +k ,W t+k )>δ then (cid:46) Dist.func.d2 MLmodelsarenotinitializedfromacoldstart—aninitializa-
tion sampled randomly from a particular distribution. Indeed,
33: return FAIL
it is common to start training from a set of weights that have
34: return Success
previously achieved good results on a different dataset, and
improve upon them (a warm start). A common example of a
warm start is transfer learning [67]. If we do not check the
the purported and the calculated updates is independent from
W ⇒ W(cid:48) . Additionally, due to the Markovian provenance of the initial state, we discuss in § VII how an
e,(q) e,(q)+k adversarialprovercouldfine-tuneastolenmodelbycontinuing
nature of the gradient descent process (see § VI), the updates
W(cid:48) ⇒ W(cid:48) are independent of each other. Com- totrainitforafewsteps,thuscreatingavalidPoL,andclaim
e,(q)+i e,(q)+i+1 that they have started from a lucky initialization—where the
bining the last two factors, we have:
lucky initialization is the true owner’s final weights.
VSR(V,P(f
WT
))=Pr[VERIFY[W,φ]=1]
sta
I
r
n
t w
or
h
d
i
e
le
r
d
to
efe
e
n
s
d
ta
i
b
n
l
g
ish
aga
th
in
e
st
Po
th
L
e
i
s
n
aid
mo
a
d
tt
e
a
l
c
s
k
w
s
i
c
t
e
h
na
a
rio
w
,
a
w
rm
e
E Q
(cid:89)(cid:89) propose to establish a chain of trust: a PoL P(f ) should
= Pr[dist ≤δ |φ]·Pr[Tr |φ] WT
e,(q)+k e,(q),k come with a previously published P0, where P0 denotes the
e=1q=1
proof needed to verify the initial state W used to obtain
0
E Q k−1
(cid:89)(cid:89) (cid:89) P(f ). The verifier keeps a record of previously verified
= Pr[dist ≤δ |φ] Pr[Tr |φ] (3) WT
e,(q)+k e,(q)+i,1 proofs. Therefore, in Algorithm 2, if V has recorded P0,
e=1q=1 i=0
VERIFYINITPROOF would be a simple record lookup. Other-
Note that in above W(cid:48) = W , as V is given these wise, it would trigger the verification of P0. The verification
weights,sononoiseis
e
in
,(
t
q
r
)
o
+
d
0
ucedby
e,
r
(
e
q)
producedcomputation. success rate follows a chain rule VSR
(cid:0)
V,P(f
)→P0(cid:1)
=
We observe that decreasing the checkpointing interval or in- VSR(V,P(f ))VSR
(cid:0) V,P0(cid:1)
,where→deno
W
te
T
sthedepen-
WT
creasingthequerybudgetperepochQaddstotheprobability dence.OfcourseP0 candependonapriorPoLP1,andsoon.

--- 第 8 页 ---
8
Concretely,forallj ≥0,letPj+1 denotethePoLforthefirst assumption is used in ML libraries, including pytorch [71]
set of model weight needed to obtain Pj, and P0 =P(f ), and tensorflow [72], to enable in-place model updates.
W0
i.e., P0 is the proof for W . Therefore, the VSR for a chain Gradient-basedstochasticoptimizationmethodisnotonlya
0
of R prior PoLs can be written as: Markovprocessbutalsostationary,assumingthatanyrandom-
ness in the architecture is fixed (e.g., using a fixed batching
VSR (cid:0) V,P(f WT )→P0 →···→PR(cid:1) strategy, and with deterministic dropout masks). Without loss
R of generality, we prove this property for SGD but note that
=VSR(V,P(f ))
(cid:89)
VSR
(cid:0) V,Pj(cid:1)
. (5) other gradient-based stochastic optimization methods follow
WT
j=0 (G4). Here, we adopt the notation W˜ t :=(W t ,M t ) to denote
themodelweightandtheassociatedlearninghyperparameters
If T cannot provide such a proof, then it must be the case
at step t. Thus, a training step is represented as follows:
that they have trained a model starting from a random initial
state. In this case, T should provide their initialization distri- W˜ =W˜ −η∇ Lˆ +z , (6)
bution and strategy and apply a statistical test to verify that
t+1 t W˜
t
t t
the initial model parameter values contained within the proof wherez t istherandomvariablerepresentingnoisearisingfrom
sequence were indeed sampled from the claimed distribution. the hardware and low-level libraries such as cuDNN [73] at
4) Verifying Initialization: Most existing initialization step t and the set of random variables {z t | t ∈ [T]} are
strategies for model weights such as Xavier [57], Kaim- independent and identically distributed. Thus, for all steps t
ing [59], and Orthogonal Initialization [68], involve sampling and arbitrary w˜ a ,w˜ b ,
values from a designated distribution (usually normal or Pr(W˜ =w˜ |W˜ =w˜ )=Pr(W˜ =w˜ |W˜ =w˜ ).
t+1 a t b t a t−1 b
uniform). Such distributions rely on the architecture of the
model (e.g., dimensionality of a certain layer), so it can be Thus, the process of training a neural network using gradient-
easily obtained given the initialization strategy which must be based stochastic optimization is a stationary Markov process.
included in the initial metadata M ∈M.
0
TheKolmogorov–Smirnov(KS)test[69]isastatisticaltest
B. Entropy Growth
to check whether samples come from a specific distribution.
Building on our results in § VI-A, we analyze the entropy
We use a single-trial KS test to check if the weights of
growth of training a DNN as a stationary Markov process,
each layer are sampled from the designed distribution. If any
Θ =W˜ ,··· ,W˜ .Entropycapturesthevariance,ornumber
layer does not pass the KS test, i.e., the p-value is below T 0 T
ofpossiblepathsofthegradientdescentsequences[74].Using
the chosen significance level, the verifier can claim that the
the definition of entropy rate (refer Equation 13 Appendix A)
initialization parameters are not sampled from the prover’s
and Markovian nature of the training process Θ , we get the
claimedinitializationdistribution,makingthePoLinvalid.We T
entropy rate as follows:
note that the tests are done under the assumption that the
different layers are initialized independently which is often H(cid:48)(Θ )= lim H(W˜ |W˜ ,...,W˜ )=H(W˜ |W˜ ) (7)
T T 0 T−1 1 0
the case [70]. Otherwise, the significance level should be T→∞
corrected to account for multiple testing using a method such =H(z 0 ) (8)
as Bonferroni’s method. Along with all other metadata (e.g.,
where we obtain Equation (8) by plugging in the result stated
the optimizer), we assume that T and A must choose an
in Equation (6). This proves the following result:
initialization strategy from a previously chosen (and publicly
known) set of strategies (e.g., all widely-known strategies), Theorem 1 (Entropy Growth). The entropy of the training
preventing the adversary from creating an arbitrary initializa- process Θ grows linearly in the number of training steps T.
T
tion strategy for their own spoofing purposes. In Algorithm 2,
VERIFYINITIALIZATION handles the initialization test.
To bound the entropy, our verification scheme performs a
step-wise comparison. Otherwise, the entropy would grow
VI. CORRECTNESSANALYSISOFTHEGRADIENT unbounded, increasing the difficult of accurately verifying
DESCENTMECHANISMFORPROOF-OF-LEARNING the updates in a training process. Further, Theorem 1 also
Recall that the goal of our proposed verification scheme proves that the exact reproducibility of a ML model is
is for the verifier to gain confidence that each of the steps difficult because the entropy grows, without bound, as
recorded in the PoL are valid, rather than verifying the end- the training sequence grows (rendering retraining-based
to-endsequencealtogether.Wenowprovewhytheverification spoofing impossible). This result holds true even with an
must be performed step-wise. identical initialization and batching strategy. Note that our
only assumption was the presence of some i.i.d noise in
the training process arising due to hardware and low-level
A. Stationary Markov Process
libraries. Our result is therefore of interest beyond the setting
Training a neural network using a gradient-based stochastic consideredinourwork,andinparticularexplainsthenegative
optimization method is a Markov process, i.e., its future resultsobservedpreviouslyinmodelextractionresearchwhen
progression is independent of its history given its current trying to reproduce a training run exactly [75].
state. We formalize this property in Appendix A. The Markov

--- 第 9 页 ---
9
CheckpointingInterval,k Deterministic
Interpretation of Entropy Growth. Recall the definition
k=E·S k=1 operations
of entropy [74]. The entropy of a training step captures the
variance, or number of possible paths from that state (i.e.,
howmuchinformationisneededtodescribethepossibilities).
Thus, the expected variance of the sequences grows too. The
relation between entropy and number of possible sequences
ispredominantlyexponentialasitsdefinitionislogarithmicto
theprobability.ThusthelineargrowthinentropyinTheorem1
represents an exponential growth in the number of potential
sequences of gradient descent.
C. Reproducibility Evaluation
To illustrate our analysis, we empirically evaluate our ver-
ification scheme and the implications of Theorem 1. We also
discussed how to configure hyperparameters of Algorithms 1
and 2 to analyze trade-offs between storage cost and correct-
ness of PoL verification.
1) Experimental Setup: A Residual Neural Network
(ResNet) [76] is a common deep neural network architecture
used for image classification. We evaluated the proposed PoL
for ResNet-20 and ResNet-50 on two object classification
tasks:CIFAR-10andCIFAR-100[77]respectively.Eachofthe
twodatasetsiscomposedof50,000trainingimagesand10,000
testingimages,eachofsize32×32×3.Thedatasetsdifferin
that CIFAR-10 only has 10 classes whereas CIFAR-100 has
100 classes. Thus classifying CIFAR-100 is considered as a
hardertask.Bothmodelsaretrainedfor200epochswithbatch
size being 128 (i.e., E =200, S =390).
2) Metrics For Evaluation: Our goal here is to understand
how the entropy growth of training (see Theorem 1) impacts
our capability to verify a training update. Formally, we are
given (initial) weights W which are trained to a state W ,
t t+k
where k represents some previously chosen and fixed check-
pointing interval. The verifier then attempts to reproduce this
stepbycalculatingtheirownW(cid:48) fromW .Thereproduction t+k t
error here is ε (t) =d(W ,W(cid:48) ), using some distance repr t+k t+k
metric d, e.g., a p-norm. With a sufficiently small ε (t), repr
∀t ∈ [T], a verifier can confirm that indeed W(cid:48) ≈ W ,
t+k t+k
∀t∈[T],whichprovesthattheprovertrainedthisMLmodel.
Specifically, we require that max ε (t) (cid:28) d , where d t repr ref ref
= d(W1,W2) is the reference distance between two models
T T
W1 and W2 of the same architecture, trained to completion
T T
(i.e., for T steps) using the same architecture, dataset, and
initialization strategy, but with a different batching strategy
and not forcing the same initial parameters (i.e., W1 (cid:54)=W2).
0 0
If this is the case, then we can set our distance threshold δ
(refer to Algorithm 2) such that max (ε (t) ) < δ < d .
t repr ref
Notethatd canbeinterpretedasthedifferencebetweentwo
ref
models trained from scratch by two independent parties, so it
isusedasourupperbound(i.e.,iftwomodelsdifferbyabout
d then they should not be considered as related).
ref
ObservingTableI,weseethatourempiricalresultscorrob-
orate Theorem 1. Reproducing weights trained step by step
(k = 1) leads to a negligible ε (t). However, attempting
repr
to reproduce an entire sequence leads to a large error due
to the linear increase in entropy over the T steps. Note
that this error accumulates even when using the exact same
||)t(rperε|| (cid:96)1 0.974(±0.004) 0.001(±0.001) 0.582(±0.004)
(cid:96)2 0.955(±0.004) 0.001(±0.001) 0.569(±0.004)
(cid:96)∞ 0.769(±0.052) 0.001(±0.001) 0.307(±0.035)
cos 0.914(±0.007) 0.0(±0.0) 0.46(±0.007)
(a) CIFAR-10
CheckpointingInterval,k Deterministic
k=E·S k=1 operations
||)t(rperε|| (cid:96)1 0.903(±0.002) 0.002(±0.001) 0.903(±0.001)
(cid:96)2 0.815(±0.002) 0.002(±0.002) 0.816(±0.001)
(cid:96)∞ 0.532(±0.07) 0.004(±0.004) 0.51(±0.055)
cos 0.383(±0.002) 0.0(±0.0) 0.384(±0.002)
(b) CIFAR-100
TABLEI:Normalizedreproductionerror,||ε (t)||,ofavalid
repr
PoL. The same initial parameter values W , batching strategy,
0
model architecture, and training strategy are used. W(cid:48) is
t+k
reproduced from W by retraining ∀t ∈ {0,k,2k,...,T}
t
while ||ε (t)|| is computed as the distance between W(cid:48)
repr t+k
and W normalized by d (see Table V in Appendix E for
t+k ref
exact values of d ). Deterministic operations used k =E·S.
ref
batchingstrategy,architecture,initialparameters,andtraining
setup, due to the irreproducible noise z arising from the
hardware and low-level libraries. Thus, it is impossible for
a verifier to reproduce an entire training sequence and we
require that k be sufficiently small to prevent these errors
from accumulating and approaching to d . Note that our
ref
results display a normalized ||ε (t)|| = maxT(εrepr) where repr dref
we require that ||ε (t)|| <<1 for the sufficient condition to
repr
hold so that the verifier can select a suitable δ.
3) Deterministic Operations: Libraries such as pytorch
provide functionality that restrict the amount of random-
ness [78] (e.g., using deterministic algorithms for convolution
operations) to enable reproducibility. We evaluate this with
k = T (refer Table I). As seen in Table I, ||ε || with repr
deterministic operations drops to half of ||ε || for non- repr
deterministic operations with ResNet-20. However, ||ε || repr
is still significant and deterministic operations incur a large
computational cost in training and a greater than one per-
centage point accuracy drop. The reduction in ||ε || is not repr
observed for ResNet-50, which is likely because the main
source of randomness for this architecture is not captured by
deterministic operations provided by pytorch. Some other
libraries use counter-based pseudorandom number generators,
which will be discussed in § VIII.
4) Checkpointing Interval and Storage Cost: The check-
pointing interval k is a hyperparameter of the proposed PoL
method and is related to the storage cost, as the prover needs
to checkpoint after every k training steps. Common practice
when training DNNs is to checkpoint at every epoch (i.e.,
k = S) to allow resuming training and pick the model
with highest accuracy after training, so we consider k = S
as a baseline and define the storage overhead as S. The
k
relationships between ||ε || and k, and ||ε || and S are
repr repr k
shown in Figure 1 and 2 respectively. The most important
observationfromthesefiguresisthattheproverdoesnotneed
to spend additional storage to save at every step, i.e., k = 1

--- 第 10 页 ---
10
   
   
   
   
                 
 & K H F N S R L Q W L Q J  , Q W H U Y D O   N 
)||rper
||(xam
  
||Wt,W0t||1
||Wt,W0t||2
cos(Wt,W0t)   
||Wt,W0t||
 
 
                 
 & K H F N S R L Q W L Q J  , Q W H U Y D O   N 
(a) CIFAR-10
)||rper
||(xam
||Wt,W0t||1
||Wt,W0t||2
cos(Wt,W0t)
||Wt,W0t||
(b) CIFAR-100
Fig.1:Normalizedreproductionerror,||ε ||,asafunctionof
repr
the checkpoint interval, k. After choosing k, δ in Alg. 2 must
be greater than ε (k). Here, we define cos = 1−cosine
repr
similarity.
    
    
    
    
    
 ×    ×    ×    ×    ×
 6 W R U D J H  2 Y H U K H D G
)||rper
||(xam
  
||Wt,W0t||1
||Wt,W0t||2
cos(Wt,W0t)   
||Wt,W0t||
 
 
 ×    ×    ×    ×    ×
 6 W R U D J H  2 Y H U K H D G
(a) CIFAR-10
)||rper
||(xam
    
   
   
   
   
                        
 / H D U Q L Q J  5 D W H    
||Wt,W0t||1
||Wt,W0t||2
cos(Wt,W0t)
||Wt,W0t||
(b) CIFAR-100
Fig. 2: Relation between ||ε || and storage overhead, where
repr
storage overhead is defined as the required number of check-
pointsdividingbythenumberofepochs(assumingtheprover
checkpoints at every epoch even if not creating PoL). It
can be seen ||ε || is still significantly lower than 1 when
repr
storage overhead = 1× (i.e., no storage overhead). Figure 6 in
Appendix E shows sample values in megabytes.
suffices.Inparticular,iftheproveronlyutilizesthecheckpoints
saved roughly at every epoch (k ≈ S), they can still attain
||ε || substantially below ||ε || ≈1 for k =T. In Figures
repr repr
1, 2 and Table I for the CIFAR-10 dataset, we observe that
using k =S outperforms creating PoL with the deterministic
operations described in § VI-C3 and does not influence the
speed of training or model’s accuracy. Note that the prover
couldalsosavethecheckpointswithaprecisionoffloat16
rather than float32 to save a factor of 2 in storage (please
see § VIII for details on related storage considerations).
5) Varying Learning Rate: Since the proposed PoL relies
ongradientupdates,||ε ||iscorrelatedtolearningrateη,the
repr
hyperparameter that controls magnitude of gradient updates.
Thuswepresenttherelationbetween||ε ||andηinFigure3.
repr
Itcanbeseenη hasasignificantimpacton||ε ||onlywhen
repr
it is set to 1. This may be because when η is too large, the
training process is unstable so a tiny difference may lead to
distinct parameters after a few steps.
D. Initialization Verification
As described in § V-D3 and § V-D4, if a prover claims
their model is trained from cold-start (i.e., rather than from
pre-trainedweights),aKStestisappliedtoverifywhetherthe
)||rper
||(xam
||Wt,W0t||1
||Wt,W0t||2    
cos(Wt,W0t)
||Wt,W0t||
   
 
                        
 / H D U Q L Q J  5 D W H    
(a) CIFAR-10
)||rper
||(xam
||Wt,W0t||1
||Wt,W0t||2
cos(Wt,W0t)
||Wt,W0t||
(b) CIFAR-100
Fig. 3: Influence of learning rate, η, on ||ε || (k = 12): if
repr
η is in the order of magnitude smaller than 100, η does not
have significant impact on ε . However, when η is set to
repr
1, ||ε || increases significantly.
repr
CIFAR-10 CIFAR-100
Step 7.00(±3.87) 1(±0)
Accuracy 10.526(±0.953)% 1.124(±0.348)%
TABLE II: Index of the training step that p-values of the KS
test dropped below the significance level, α = 0.01, and the
correspondingvalidationaccuracy.Afterthisstep,atleastone
layer is statistically different from a newly initialized layer.
initial state in the PoL is sampled from a random distribution
per the claimed initialization strategy.
Usingthesamesetupasin§VI-C1,weappliedaKStestto
the early training steps (with S = 390 for both datasets). As
shown in Figure 4, for both models, the minimum p-value
across all network layers drops to 0 rapidly. We interpret
this as: the weight distribution for at least one of the layers
is statistically different from the initialization distribution.
Observing Table II, 7 updates of ResNet-20 and 1 update of
ResNet-50onaveragewouldleadtop-valuebelow0.01,where
the validation accuracy is only slightly higher than random
guessing (i.e., 10% for CIFAR-10 and 1% for CIFAR-100).
VII. SECURITYANALYSISOFTHEGRADIENTDESCENT
MECHANISMFORPROOF-OF-WORK
Choosing a suitably low checkpointing interval allows us
to control the entropy growth (in other words, the number
of possible sequences of gradient descent). Controlling the
entropy growth enables verification of the PoL: the prover T
can claim ownership in our model stealing scenario, or the
model owner can trust the parameters shared by a worker in
thedistributedlearningscenario(see§IV).Hereweshowthat
in addition, the entropy growth also creates an asymmetry
between the adversary A and verifier V. This asymmetry
disadvantages A trying to spoof the PoL to pass verification
with lesser computational effort i.e., a structurally correct
spoof. In light of this observation, we introduce and analyze
two classes of spoofing strategies.
A. Defining a Spoof
Recall from § IV-A that A has gained access to f (i.e.,
WT
its weights) but does not have a PoL that passes verification.
ThusAmustcreateaspoofP(A,f)provingthattheytrained

--- 第 11 页 ---
11
   
   
   
                    
 7 U D L Q L Q J  6 W H S
 H X O D Y  S
 $ Y H U D J H    
 0 L Q L P X P
   
   
   
   
                
 7 U D L Q L Q J  6 W H S
(a) CIFAR-10
 H X O D Y  S
To construct a complete PoL and pass verification, an
 $ Y H U D J H adversary will iteratively repeat this inverse step and solve
 0 L Q L P X P
Equation (9) until they obtain a suitable W that can be
0
justified to have been either (a) sampled from a random
distribution or (b) accompanied with a valid PoL P0 in the
chain-of-trust setting (see §V-D3). We call this process of
obtaining initial weights W from the final weights W the
0 T
inverse gradient method.
This approach is analogous to using the Euler Backward
(b) CIFAR-100
method to iteratively maximize the loss function, and is not
Fig. 4: p-value of Kolmogorov–Smirnov test with the null
new to ML in general [79]. However, to the best of our
hypothesis that the model parameters came from the claimed
knowledge,itisnewtoDNNs,andwecallitbyanewnameas
initialization distributions, with respect to number of training
to emphasize the context we are using it in; we are using this
steps: one can observe the minimum drops to almost zero
as an inverse procedure. As we will show, the top-Q strategy
withinafewsteps,meaningat leastonelayerhasweightsout
of verification (refer Algorithm 2) will prevent this spoof.
of the initialization distributions. 1) Entropy for the Inverse Gradient Process: Recall from
Theorem 1 that the forward process has a linearly increasing
entropy growth with respect to the total number of training
the model f, where f is an approximation of f (denoted
WT steps T. We now prove that the inverse gradient process is
f ≈ f ), and has comparable test-time performance (see
WT lower-bounded by this increase. Recall Equation (6) which
§VII-B3bandVII-C1).Anadversarymayalways(re)perform
accounts for noise in SGD. To formulate the rate of entropy
the training required to obtain f. We call this an honest spoof
growth per inverse step, we take the conditional probabilities
because E[C ] ≥ E[C ]. Thus, the adversary gains nothing
A T of W with respect to W , as it was computed previously:
computationally beneficial from having gained access to f t−1 t
WT
and our verification scheme satisfies Property 2. H(W˜ |W˜ )=H(z )+H(η∇ L|W˜ ) (10)
t−1 t 0 W˜
t−1
t
Definition 2 (Dishonest Spoof). Any spoof for a prover’s The inverse gradient process thus has higher entropy than the
model f that passes verification, and where the adversary forward process if and only if H(η∇ L|W˜ )>0. This is
WT W˜
t−1
t
expends fewer computational resources than the trainer, i.e., true if and only if our inverse step (Equation (9)) has more
E[C ]<E[C ], is dishonest. than one solution with non-zero probability. That is, there is
A T
more than one training path using η that reaches weights W .
Intuitively, for an attack to be dishonest, the adversary t
would need to leverage knowing f in order to possibly Theorem 2 (ReverseEntropyGrowth). SimilartoTheorem1,
WT
construct a PoL for f using less computational resources theunconstrainedreversetrainingprocess,denotedbyΘ =
WT −T
than the T. Knowing the architecture of f does not inform {W ,W ,··· ,W }, is also a Markov random process. It
T T−1 0
one on any part of a PoL other than the model one computed hasequalorgreaterentropythantheforwardtrainingprocess
gradientson.HoweverW isthelaststateinW;thus,whatwe Θ , that is H(Θ ) ≥ H(Θ ), with equality if and only if
T T −T T
willconsiderarespoofingattacksthatleverageknowingW to ∇ L|W˜ is deterministic.
T W˜
t−1
t
constructaPoL.Wewillcallspoofingattacksthatuseknowing
If the necessary and sufficient condition is true, then we
W to make training less onerous as directed retraining and
T necessarily have that the rate of entropy accumulation in
those that attempt to reconstruct a PoL backwards starting
inverting a training step is greater than the rate of entropy
from W as inverse gradient methods. These two methods
T accumulation in the forward process: we would expect to see
encapsulate the two directions one could realistically obtain
greatervarianceinourinversepathsthaninourforwardpaths.
any ordered sequence (i.e., a structurally correct PoL that
Given the large confidence intervals in Figures 11 and 13b
may or may not pass verification): forwards (i.e., directed
(seeVII-B3forexperimentalsetup),wehypothesizethatthese
retraining) and backwards (i.e., inverse gradient methods).
necessaryandsufficientconditionsaretrueforDNN,i.e.,there
are several training paths passing through the same weights.
B. Inverse Gradient Methods
We leave to future work the rigorous verification of these
Recall that Equation (1) defines a training step with SGD: conditionsbecausetheyarenotnecessarytorefutetheinverse
given weights W t−1 we find the next set of weights W t . The gradient-based spoofing attacks that we propose.
inverse gradient step solves the inverse problem to this: given 2) Retraining-based Spoofing: Here we show why an in-
W t find the W t−1 that led to it. We will denote this problem verse gradient approach is not effective to exactly reconstruct
as solving β(W t−1 ), where β(W t−1 ) is defined as: a spoof, i.e., perform retraining-based spoofing to obtain
P(A,f )=P(T,f ).FromTheorem2weknowthatthe
β(W t−1 ):=W t−1 −W t −η∇ Wt−1 L=0 (9) entropy W o T finvertinga W se T quenceH(Θ )islowerboundedby
−T
Note that the batches these gradients are computed on do not the entropy of training the sequence H(Θ ), which we know
T
necessarilyhavetobethesameasthoseusedintraining,which grows linearly with T. Recall from § VI-B that this entropy
is necessary as we do not assume the adversary has access to represents an exponential increase in the number of paths to
the batching strategy used in training (see § IV-A). reach W . As DNN training requires thousands of steps, we
T

--- 第 12 页 ---
12
CIFAR-10 CIFAR-100
can safely say that the probability of following any given
path is near-zero. Thus for any sequence sufficiently long,
i.e., T (cid:29) 0, we can dismiss the inverse gradient method for
a reconstruction spoof because the probability of recreating a
specificsequenceisnegligible,i.e.,≈0.Indeed,ourresultsfor
reproducability(seeTableI)showempiricallythatthelengths
used for training a DNN satisfy this condition.
3) Stochastic Spoofing: To overcome the challenges of
exactly recreating P(T,f ), an adversary employing the WT
general inverse spoof instead focuses on obtaining a different
PoLP(A,f)(cid:54)=P(T,f )thatregardlesspassesverification.
WT
As we show, this is not beneficial as the adversary still faces
a computational cost at least as large as that for T and it is
difficult to end in a suitable random initialization.
a) TheComputationalCosts: Anynumericalapproachto
solving Equation (9) will require at least one function call to
Equation(9),e.g.,tocheckthattheprovidedsolutionisindeed
the correct solution. Since computing Equation (9) requires
computing∇ L,i.e.,,onetrainingstep,invertingatraining
Wt−1
step is bounded by the computational load of a training step.
We remark that DNNs are highly non-linear and as such
there are no known analytical solutions to Equation (9). Thus
attemptingtocreateaPoLsuchthatP(A,f )(cid:54)=P(T,f )
WT WT
but that passes verification would be at least as computation-
ally expensive as what it took T.
The only remaining strategy to make the computational
costsfeasible,whilemaintainingf =f ,isforanadversary
WT
to take larger inverse steps, i.e., use larger learning rates
so as to reduce the length of the PoL. To disprove this
we conducted experiments on a LeNet5 model [80] on the
MNISTdataset[81].Thefirstsetofexperimentscomparedthe
effect of the learning rate to reconstruction error ε after
repr
each step t (see Figures 13b, 13a, and 15), and the second
compared the effect of fewer and more iterations of the root
solver for moderate learning rates (see Figure 11). We ran
all these experiments inverting 50 steps (with k = 1 ) from
a state achieved after 5 epochs of training. All experiments
are repeated 35 times to capture variance as seen in the
confidence intervals. We further evaluated on ResNet models
on CIFAR-10 and CIFAR-100, the experimental setup of
which is described earlier in § VI-C1.
As seen from these experiments, the reproducability error
(theerrorbetweenwhereatrainingstepfromW leadsand
t−1
W )quicklyincreasesafterafewstepsforlearningratesabove
t
10−4, meaning the PoL obtained is not valid. As this was the
caseforarelativelysmallmodel,wealsoexpectthistobethe
case for larger models; our tests on inverting ResNet models
also resulted in average ε larger than those found when
repr
training with k = 1 (see Tables I and III). Thus, we have
empirically determined that an adversary cannot use higher
learning rates to decrease the computational load.
From the argument we have made (G2), and given that
we are not aware of a mechanism to prove this formally, we
present the following as a conjecture:
Conjecture 1. Inverting a training sequence using numerical
rootfindingmethodswillalwaysbeatleastascomputationally
expensive as training, given the same model.
||rperε||
(cid:96)1 0.023±0.001 0.005±0.001
(cid:96)2 0.048±0.004 0.016±0.005
(cid:96)∞ 0.18±0.044 0.073±0.014
cos 0.016±0.002 0.0±0.0
TABLE III: Normalized reproduction error, ||ε || of PoL
repr
created by General Inverse Gradient Method. The trained
modelsinvertedfor50stepstoobtainaPoLwithlength50and
k = 1. The ε is then computed on this PoL. Comparing
repr
to the k =1 case in Table I, the ε here is larger. repr
b) Difficulty of Finding a Suitable Initialization: As
mentioned in § V-D4, a valid initialization must pass the KS
test [69]. To test the initialization, the verifier compares it
against the public pool of known initializations, e.g., various
formsofzero-centereduniformandnormaldistributions[57]–
[59]. Thus, the adversary must in addition successfully spoof
the initialization to pass the KS test. Our empirical results
indicate that inverse gradient methods are unlikely to find
a valid initialization. Specifically, we inverted 50 steps on
a model trained for 50 steps, and applied the KS test to
the last state of inverting (corresponding to the first state of
training) as described in § V-D4. On CIFAR-10 we observe
thatthetheaverageandminimump-valuesare0.044(±0.102)
and 1.077(±1.864)×10−28,respectively.On CIFAR-100,the
averageandminimump-valuesare0.583(±7.765)×10−12and
0(±0),respectively.Thesep-valuesarefarbelowtherequired
thresholdtopasstheKStestandthusanadversaryisunableto
find a valid initialization sampled from a claimed distribution.
A clever adversary may attempt to direct the inverse gradient
method toward a valid initialization. We discuss in § VII-C
belowhowthesedirectedapproachesdonotsucceedinpassing
our verification scheme. We remark that the KS test prevents
other spoofing strategies, such as leveraging fine-pruning [82]
or sparsification [83]. These strategies can significantly min-
imize the computational load of spoofing while maintaining
both the model architecture and test-time performance, i.e.,
f ≈f . However, they as well fail to pass the KS test and WT
thus are not verified by our scheme.
C. Directed Retraining
Given no extra knowledge, retraining f would take as
WT
much compute as used by T. However, the adversary always
has the additional advantage of knowing the final weights
W . We now explore how the adversary can leverage this
T
knowledge to create a dishonest spoof (see Definition 2).
1) Approach1:PoLConcatenation: AnadversaryAaware
that V does not verify all the updates may try to exploit this
and employ structurally correct spoofing (refer § IV-A) to
obtain a partially valid PoL that may pass the verification. To
thisend,theadversarycanfine-tune[67]orfine-prune[82]the
model f to achieve f which is not an exact copy of f
WT WT
but has comparable test-time performance. This step provides
the adversary with a valid PoL from f to f. However, this
WT
would still be detected by Algorithm 2 because V also checks
the initial state (recall § V-D4), which in the adversary’s PoL
is W (for which it has no valid PoL).
T

--- 第 13 页 ---
13
    
    
    
    
    
                         
 7 U D L Q L Q J  6 W H S V
 H F Q D W V L '
 
||WT,W0s||2  
 P D [||W0t,W0t 1||2
 
 
                         
 7 U D L Q L Q J  6 W H S V
(a) CIFAR-10
 H F Q D W V L '
performance periodically since the arbitrarily large updates
||WT,W0s||2 would likely decrease model performance significantly.
 P D [||W0t,W0t 1||2 2) Approach 2: Directed Weight Minimization: To mini-
mize the discontinuity magnitude, an adversary may attempt
todirecttheweightsofretrainingtowardW .Toachievethis, T
they can directly minimize this distance using regularization.
Thisapproachfailsverificationbecausethecustomregularizer
requires the final weights prior to them having been achieved,
whichthereforecannotpassverification.Further,thisinforma-
(b) CIFAR-100
tion cannot be easily distilled into synthetic data because no
Fig. 5: Magnitude of discontinuity ||W −W(cid:48)|| and largest
T s 2 gradient of the regularization term, with respect to the data,
valid update max||W(cid:48)−W(cid:48) || in a spoofing PoL made by
t t−1 2 exists (refer to Appendix B for more details). By this vain,
concatenating 2 valid but independent PoL. The discontinuity other tactics, such as optimizing a learning rate η to converge
is significantly larger than the valid updates, and thus easily W(cid:48) to W also fail verification.
T
detectedbyAlgorithm2whichchecksthelargestupdatesfirst.
VIII. DISCUSSIONS&LIMITATIONS
A PoL provides grounds for proving ownership of any
To adapt, the adversary can train a model with the same
effortful attempt at learning a model. As shown in § VI, a
architecture as f from a random initialization for some
WT PoL guarantees that no one but the trainer can lay claim to
number of steps with minimal cost, providing a second valid
that exact model. Further, if a chain-of-trust is adopted, this
PoL, this time starting from a valid random initialization.
guarantee is extended to the use of the said model as an
Then, the adversary concatenates these two PoLs. In addition
initial state for the training of a surrogate model. However,
to saving compute, the advantage of this strategy is that
a PoL cannot be used to connect the model to its surrogate,
there is only one single point of discontinuity in the PoL,
neithercanitbeusedavoidextraction.Instead,aPoLprovides
which consists of thousands of updates. Thus if V randomly
legal protection: if the trainer produces a PoL and publishes a
sampled a few updates to check, the A’s PoL would likely
time-stamped signature of it, this unchangeable record proves
go undetected. However, since V verifies the top-Q updates
ownership in case of false claim by a surrogate model owner.
in Algorithm 2, this discontinuity which is among the largest
We now discuss limitations with our proposed scheme for
of the sequence would be invalidated—as we evaluate next.
PoL. First, our verification scheme requires that the training
data be shared with the verifier. When this data is private, this
Evaluation. Ourevaluationisperformedwiththesetupfrom
can be undesirable. To protect the training data’s confidential-
§VI-C1.Foreachdataset,wefirsttrainamodeltocompletion
ity,itispossiblefortheprovertoengageinaprivateinference
as the prover T’s model W . Then we play the role of A to
T protocol with the verifier [84] using multi-party computation.
spoof a PoL by concatenation: we fine-tune W for 1 epoch
T This will incur additional computational overhead but is only
togetf,andtrainanothermodel(fromscratch)withthesame
limited on the chosen private inference scheme.
architectureforssteps(s≤T)frominitialization(i.e.,W(cid:48) to
0 Second, we note the considerable storage requirements our
W(cid:48));sisthenumberofstepsonthex-axisinFigure5.Weplot
s proposed proof-of-work imposes. To decrease the approach’s
||W −W(cid:48)|| and max ||W(cid:48)−W(cid:48) || (both normalized
T s 2 t≤s t t−k 2 footprint by a factor of 2, we downcast the float32 values
(by d )) in this figure (with k =1). We observe that:
ref of our parameters to float16 when saving them. Verifying
• Thediscontinuity(i.e.,||W T −W s (cid:48)|| 2 )ismuchlargerthan float16 values introduces minimal error. We acknowledge
all valid gradient updates in the PoL, so setting Q = 1 that other approaches such as hashing could provide signifi-
wouldbesufficientfortheverifiertodetectthisspoofing. cantly better improvement to the storage footprint. For exam-
TheverificationcostisE·k =Estepsofgradientupdates ple,followupworkmayconsiderhashingweightssequentially
(since we set k =1 for this experiment). However, if the utilizing Merkle tree structure [85], i.e. each consecutive set
verifier randomly samples E steps (rather than picking of weights during the training procedure are hashed and then
the top-1 step of every epoch), the probability of finding saved as the hash of the concatenation of the current weights
the discontinuity is only 1, with S =390 here. and the previously saved hash. We do not use Merkle trees
S
• Thediscontinuityhassimilarmagnitudetod ref ,revealing due to the error accumulated when the verifier reconstructs
the fact that W T and W s (cid:48) are unrelated. the weights: the error in the weights forces the weights of
• max t≤s ||W t (cid:48)−W t (cid:48) −k || 2 does vary significantly with re- the verifier and legitimate worker to hash to different values,
specttos,meaningsettingδ to||W t (cid:48)−W t (cid:48) −k || 2 forsmall losingtheabilitytoverifythattheweightsmatchwithinsome
t is sufficient to detect this kind of attack. bound.Thismaybeaddressedwithfuzzyextractorsorlocality
It is worth noting that if the adversary A has knowledge sensitive hashing (LSH). However, the use of fuzzy extractors
aboutQ,orverifierV setsQtoasmallvalue,AmaymakeQ and LSH protocols incurs significant difficulty through the
(ormore)legitimateupdatesineveryepochbytrainingwithan need to find a suitable bound to work over all choices of E,
arbitrarily large learning rate, which will bypass Algorithm 2. Q, and k. Designing such primitives is future work.
Solutions to this issue could involve (a) using a large Q, (b) Third, we emphasize that counter-based pseudorandom
randomlyverifyingsomemoreupdates,or(c)checkingmodel number generators [86], [87] can potentially remove most, if

--- 第 14 页 ---
14
notall,noiseinthetrainingprocessbecausethepseudorandom [5] F.Trame`r,F.Zhang,A.Juels,M.K.Reiter,andT.Ristenpart,“Steal-
numbers are generated based only off the input seed, not ingmachinelearningmodelsviapredictionapis,”in25th{USENIX}
SecuritySymposium({USENIX}Security16),2016,pp.601–618.
any hardware-based source of entropy. Recall that this noise
[6] N. Papernot, P. McDaniel, I. Goodfellow, S. Jha, Z. Berkay Celik,
introduces the random variable z in Theorems 1 and 2. While andA.Swami,“PracticalBlack-BoxAttacksagainstMachineLearn-
thereiscurrentlynoground-truthforallsourcesofrandomness ing,”arXive-prints,arXiv:1602.02697,arXiv:1602.02697,Feb.2016.
arXiv:1602.02697[cs.CR].
arising in ML training through hardware, low-level libraries,
[7] S. Pal, Y. Gupta, A. Shukla, A. Kanade, S. K. Shevade, and V.
and random number generation, such ground-truths would Ganapathy,“Aframeworkfortheextractionofdeepneuralnetworks
make training more reproducible and facilitate our approach. byleveragingpublicdata,”CoRR,vol.abs/1905.09165,2019.arXiv:
1905.09165.[Online].Available:http://arxiv.org/abs/1905.09165.
Finally, we remark that our probability of success for our
[8] J. R. Correia-Silva, R. F. Berriel, C. Badue, A. F. de Souza, and
verificationschemedegradesmultiplicativelywitheachusage. T.Oliveira-Santos,“Copycatcnn:Stealingknowledgebypersuading
This limits its usage for extremely long chains of PoLs (e.g., confessionwithrandomnon-labeleddata,”in2018InternationalJoint
ConferenceonNeuralNetworks(IJCNN),IEEE,2018,pp.1–8.
when successively transfer learning between many models)
[9] T. Orekondy, B. Schiele, and M. Fritz, “Knockoff nets: Stealing
where any given probability of success is significantly below functionality of black-box models,” in Proceedings of the IEEE
1.AsthereiscurrentlynoPoLschemetogainpracticalinsight Conference on Computer Vision and Pattern Recognition, 2019,
pp.4954–4963.
on this limitation, we leave this to future work.
[10] M. Li, D. G. Andersen, J. W. Park, A. J. Smola, A. Ahmed, V.
Josifovski,J.Long,E.J.Shekita,andB.-Y.Su,“Scalingdistributed
IX. CONCLUSIONS
machinelearningwiththeparameterserver,”in11th{USENIX}Sym-
posium on Operating Systems Design and Implementation ({OSDI}
Our analysis shows gradient descent naturally produces 14),2014,pp.583–598.
[11] P. Blanchard, R. Guerraoui, J. Stainer, et al., “Machine learning
secretinformationduetoitsstochasticity,andthisinformation
with adversaries: Byzantine tolerant gradient descent,” in Advances
can serve as a proof-of-learning. We find that entropy growth inNeuralInformationProcessingSystems,2017,pp.119–129.
during training creates an asymmetry between the adversary [12] C.DworkandM.Naor,“Pricingviaprocessingorcombattingjunk
mail,” in Proceedings of the 12th Annual International Cryptology
and defender which advantages the defender. Perhaps the
Conference on Advances in Cryptology, ser. CRYPTO ’92, Berlin,
strongest advantage of our approach is that it requires no Heidelberg:Springer-Verlag,1992,pp.139–147,ISBN:3540573402.
changes to the existing training procedure, and adds little [13] M. Jakobsson and A. Juels, “Proofs of work and bread pudding
protocols(extended abstract),” in Secure Information Networks, B.
overhead for the prover seeking to prove they have trained a
Preneel, Ed., Boston, MA: Springer US, 1999, pp. 258–272, ISBN:
model. We expect that future work will expand on the notion 978-1-4757-6487-1 978-0-387-35568-9. DOI: 10.1007/978-0-387-
of proof-of-learning introduced here, and propose improved 35568-9 18. [Online]. Available: http://link.springer.com/10.1007/
978-0-387-35568-9 18(visitedon11/09/2020).
mechanisms applicable beyond the two scenarios which mo-
[14] Z.Ghodsi,T.Gu,andS.Garg,“SafetyNets:Verifiableexecutionof
tivated our work (model stealing and distributed training). deepneuralnetworksonanuntrustedcloud,”p.10,
[15] D. E. Rumelhart, G. E. Hinton, and R. J. Williams, “Learning rep-
resentationsbyback-propagatingerrors,”nature,vol.323,no.6088,
ACKNOWLEDGMENTS
pp.533–536,1986.
Wethankthereviewersfortheirinsightfulfeedback.Thiswork [16] S.T.Setty,R.McPherson,A.J.Blumberg,andM.Walfish,“Making
argumentsystemsforoutsourcedcomputationpractical(sometimes).,”
wassupportedbyCIFAR(throughaCanadaCIFARAIChair),
inNDSS,vol.1,2012,p.17.
by NSERC (under the Discovery Program, NFRF Exploration [17] S. Setty, V. Vu, N. Panpalia, B. Braun, A. J. Blumberg, and M.
program, and COHESA strategic research network), and by Walfish,“Takingproof-basedverifiedcomputationafewstepscloser
topracticality,”inPresentedaspartofthe21st{USENIX}Security
gifts from Intel and Microsoft. We also thank the Vector
Symposium({USENIX}Security12),2012,pp.253–268.
Institute’s sponsors. Varun was supported in part through [18] B. Braun, A. J. Feldman, Z. Ren, S. Setty, A. J. Blumberg, and
the following US National Science Foundation grants: CNS- M. Walfish, “Verifying computations with state,” in Proceedings of
theTwenty-FourthACMSymposiumonOperatingSystemsPrinciples,
1838733, CNS-1719336, CNS-1647152, CNS-1629833 and
2013,pp.341–357.
CNS-2003129, and the Landweber fellowship. [19] C.Hawblitzel,J.Howell,M.Kapritsos,J.R.Lorch,B.Parno,M.L.
Roberts,S.Setty,andB.Zill,“Ironfleet:Provingpracticaldistributed
systemscorrect,”inProceedingsofthe25thSymposiumonOperating
REFERENCES SystemsPrinciples,2015,pp.1–17.
[1] C.Li.(Jun.3,2020).“OpenAI’sGPT-3languagemodel:Atechnical [20] C.Tan,L.Yu,J.B.Leners,andM.Walfish,“Theefficientserveraudit
overview,”LambdaBlog.LibraryCatalog:lambdalabs.com,[Online]. problem,deduplicatedre-execution,andtheweb,”inProceedingsof
Available: https://lambdalabs.com/blog/demystifying-gpt-3/ (visited the26thSymposiumonOperatingSystemsPrinciples,2017,pp.546–
on10/01/2020). 564.
[2] S.Markidis,S.W.DerChien,E.Laure,I.B.Peng,andJ.S.Vetter, [21] Y. Ishai, E. Kushilevitz, and R. Ostrovsky, “Efficient arguments
“Nvidia tensor core programmability, performance & precision,” in without short pcps,” in Twenty-Second Annual IEEE Conference on
2018IEEEInternationalParallelandDistributedProcessingSympo- ComputationalComplexity(CCC’07),IEEE,2007,pp.278–291.
siumWorkshops(IPDPSW),IEEE,2018,pp.522–531. [22] M. Walfish and A. J. Blumberg, “Verifying computations without
[3] N.P.Jouppi,C.Young,N.Patil,D.Patterson,G.Agrawal,R.Bajwa, reexecuting them,” Communications of the ACM, vol. 58, no. 2,
S. Bates, S. Bhatia, N. Boden, A. Borchers, et al., “In-datacenter pp.74–84,2015.
performanceanalysisofatensorprocessingunit,”inProceedingsof [23] M. Abadi, M. Burrows, M. Manasse, and T. Wobber, “Moderately
the44thAnnualInternationalSymposiumonComputerArchitecture, hard,memory-boundfunctions,”ACMTrans.InternetTechn.,vol.5,
2017,pp.1–12. pp.299–327,May2005.DOI:10.1145/1064340.1064341.
[4] A. Putnam, A. M. Caulfield, E. S. Chung, D. Chiou, K. Constan- [24] M. Abliz and T. Znati, “A guided tour puzzle for denial of service
tinides,J.Demme,H.Esmaeilzadeh,J.Fowers,G.P.Gopal,J.Gray, prevention,”in2009AnnualComputerSecurityApplicationsConfer-
et al., “A reconfigurable fabric for accelerating large-scale datacen- ence,2009,pp.279–288.DOI:10.1109/ACSAC.2009.33.
ter services,” in 2014 ACM/IEEE 41st International Symposium on [25] B.Waters,A.Juels,J.Halderman,andE.Felten,“Newclientpuzzle
ComputerArchitecture(ISCA),IEEE,2014,pp.13–24. outsourcing techniques for dos resistance,” Jan. 2004, pp. 246–256.
DOI:10.1145/1030083.1030117.

--- 第 15 页 ---
15
[26] F. Coelho, An (almost) constant-effort solution-verification proof-of- arXiv:2002.12200,arXiv:2002.12200,Feb.2020.arXiv:2002.12200
work protocol based on merkle trees, Cryptology ePrint Archive, [cs.CR].
Report2007/433,https://eprint.iacr.org/2007/433,2007. [48] R. Namba and J. Sakuma, “Robust Watermarking of Neural Net-
[27] ——,Exponentialmemory-boundfunctionsforproofofworkproto- workwithExponentialWeighting,”arXive-prints,arXiv:1901.06151,
cols,CryptologyePrintArchive,Report2005/356,https://eprint.iacr. arXiv:1901.06151,Jan.2019.arXiv:1901.06151[cs.CR].
org/2005/356,2005. [49] H.Li,E.Wenger,B.Y.Zhao,andH.Zheng,“PiracyResistantWater-
[28] J. Tromp, “Cuckoo cycle: A memory bound graph-theoretic proof- marksforDeepNeuralNetworks,”arXive-prints,arXiv:1910.01226,
of-work,”vol.8976,Jan.2015,pp.49–62,ISBN:978-3-662-48050-2. arXiv:1910.01226,Oct.2019.arXiv:1910.01226[cs.CR].
DOI:10.1007/978-3-662-48051-9 4. [50] H.Jia,C.A.Choquette-Choo,V.Chandrasekaran,andN.Papernot,
[29] A.Back,“Hashcash-adenialofservicecounter-measure,”Sep.2002. “EntangledWatermarksasaDefenseagainstModelExtraction,”arXiv
[30] S.Nakamoto,“Bitcoin:Apeer-to-peerelectroniccashsystem,”Cryp- e-prints, arXiv:2002.12200, arXiv:2002.12200, Feb. 2020. arXiv:
tographyMailinglistathttps://metzdowd.com,Mar.2009. 2002.12200[cs.CR].
[31] L. Huang, A. D. Joseph, B. Nelson, B. I. Rubinstein, and J. D. [51] K. Liu, B. Dolan-Gavitt, and S. Garg, “Fine-pruning: Defending
Tygar,“Adversarialmachinelearning,”inProceedingsofthe4thACM againstbackdooringattacksondeepneuralnetworks,”in21stInter-
workshoponSecurityandartificialintelligence,2011,pp.43–58. nationalSymposiumonResearchinAttacks,Intrusions,andDefenses,
[32] N. Papernot, P. McDaniel, A. Sinha, and M. P. Wellman, “Sok: 2018.
Towardsthescienceofsecurityandprivacyinmachinelearning,”in [52] B. Wang, Y. Yao, S. Shan, H. Li, B. Viswanath, H. Zheng, and B.
2018IEEEEuropeanSymposiumonSecurityandPrivacy(EuroS&P), Zhao, “Neural cleanse: Identifying and mitigating backdoor attacks
IEEE,2018. inneuralnetworks,”2019IEEESymposiumonSecurityandPrivacy
[33] B. Biggio and F. Roli, “Wild patterns: Ten years after the rise of (SP),pp.707–723,2019.
adversarialmachinelearning,”PatternRecognition,vol.84,pp.317– [53] H.Jia,C.A.Choquette-Choo,V.Chandrasekaran,andN.Papernot,
331,2018. “Entangledwatermarksasadefenseagainstmodelextraction,”in30th
[34] C.Szegedy,W.Zaremba,I.Sutskever,J.Bruna,D.Erhan,I.Goodfel- {USENIX}SecuritySymposium({USENIX}Security21),2021.
low,andR.Fergus,“Intriguingpropertiesofneuralnetworks,”arXiv [54] J. Dean, G. Corrado, R. Monga, K. Chen, M. Devin, M. Mao, M.
preprintarXiv:1312.6199,2013. Ranzato,A.Senior,P.Tucker,K.Yang,etal.,“Largescaledistributed
[35] B. Biggio, I. Corona, D. Maiorca, B. Nelson, N. Sˇrndic´, P. Laskov, deepnetworks,”Advancesinneuralinformationprocessingsystems,
G. Giacinto, and F. Roli, “Evasion attacks against machine learning vol.25,pp.1223–1231,2012.
attesttime,”inJointEuropeanconferenceonmachinelearningand [55] L. Lamport, R. Shostak, and M. Pease, “The byzantine generals
knowledgediscoveryindatabases,Springer,2013,pp.387–402. problem,” in Concurrency: the Works of Leslie Lamport, 2019,
[36] B. Biggio, B. Nelson, and P. Laskov, “Poisoning attacks against pp.203–226.
supportvectormachines,”arXivpreprintarXiv:1206.6389,2012. [56] X. Glorot, A. Bordes, and Y. Bengio, “Deep sparse rectifier neural
[37] S. Song, K. Chaudhuri, and A. D. Sarwate, “Stochastic gradient networks,”inProceedingsofthefourteenthinternationalconference
descent with differentially private updates,” in 2013 IEEE Global onartificialintelligenceandstatistics,2011,pp.315–323.
Conference on Signal and Information Processing, IEEE, 2013, [57] X. Glorot and Y. Bengio, “Understanding the difficulty of training
pp.245–248. deepfeedforwardneuralnetworks,”inProceedingsofthethirteenth
[38] L. Batina, S. Bhasin, D. Jap, and S. Picek, “CSI NN: Reverse internationalconferenceonartificialintelligenceandstatistics,2010,
engineeringofneuralnetworkarchitecturesthroughelectromagnetic pp.249–256.
sidechannel,”in28thUSENIXSecuritySymposium(USENIXSecurity [58] G. Klambauer, T. Unterthiner, A. Mayr, and S. Hochreiter, “Self-
19),SantaClara,CA:USENIXAssociation,Aug.2019,pp.515–532, normalizing neural networks,” in Advances in Neural Informa-
ISBN: 978-1-939133-06-9. [Online]. Available: https://www.usenix. tion Processing Systems, I. Guyon, U. V. Luxburg, S. Bengio,
org/conference/usenixsecurity19/presentation/batina. H. Wallach, R. Fergus, S. Vishwanathan, and R. Garnett, Eds.,
[39] M.Jagielski,N.Carlini,D.Berthelot,A.Kurakin,andN.Papernot, vol. 30, Curran Associates, Inc., 2017, pp. 971–980. [Online].
“High-FidelityExtractionofNeuralNetworkModels,”arXive-prints, Available: https://proceedings.neurips.cc/paper/2017/file/
arXiv:1909.01838,arXiv:1909.01838,Sep.2019.arXiv:1909.01838 5d44ee6f2c3f71b73125876103c8f6c4-Paper.pdf.
[cs.LG]. [59] K. He, X. Zhang, S. Ren, and J. Sun, “Delving deep into rectifiers:
[40] F.Boenisch,“ASurveyonModelWatermarkingNeuralNetworks,” Surpassing human-level performance on imagenet classification,” in
arXiv e-prints, arXiv:2009.12153, arXiv:2009.12153, Sep. 2020. Proceedings of the IEEE International Conference on Computer
arXiv:2009.12153[cs.CR]. Vision(ICCV),Dec.2015.
[41] Y. Adi, C. Baum, M. Cisse, B. Pinkas, and J. Keshet, “Turning [60] V.Vapnik,Thenatureofstatisticallearningtheory.Springerscience
your weakness into a strength: Watermarking deep neural networks &businessmedia,2013.
by backdooring,” in 27th USENIX Security Symposium (USENIX [61] I.Goodfellow,Y.Bengio,A.Courville,andY.Bengio,Deeplearning,
Security18),USENIXAssociation,Aug.2018,ISBN:978-1-939133- 2.MITpressCambridge,2016,vol.1.
04-5. [Online]. Available: https://www.usenix.org/conference/ [62] H.RobbinsandS.Monro,“Astochasticapproximationmethod,”The
usenixsecurity18/presentation/adi. annalsofmathematicalstatistics,pp.400–407,1951.
[42] J. Zhang, Z. Gu, J. Jang, H. Wu, M. P. Stoecklin, H. Huang, and [63] M.vandeZande,Leveragingzero-knowledgesuccinctargumentsof
I. Molloy, “Protecting intellectual property of deep neural networks knowledgeforefficientverificationofoutsourcedtrainingofartificial
withwatermarking,”inProceedingsofthe2018onAsiaConference neuralnetworks,May2019.[Online].Available:http://essay.utwente.
on Computer and Communications Security, 2018. DOI: 10.1145/ nl/79180/.
3196494.3196550. [Online]. Available: https://app.dimensions.ai/ [64] H. Chabanne, J. Keuffer, and R. Molva, “Embedded proofs for
details/publication/pub.1104339451. verifiable neural networks,” IACR Cryptol. ePrint Arch., vol. 2017,
[43] P. Maini, M. Yaghini, and N. Papernot, “Dataset inference: Own- p.1038,2017.[Online].Available:http://eprint.iacr.org/2017/1038.
ership resolution in machine learning,” in International Conference [65] S. Lee, H. Ko, J. Kim, and H. Oh, “Vcnn: Verifiable convolutional
on Learning Representations, 2021. [Online]. Available: https:// neuralnetwork,”IACRCryptol.ePrintArch.,vol.2020,p.584,2020.
openreview.net/forum?id=hvdKKV2yt7T. [Online].Available:https://eprint.iacr.org/2020/584.
[44] V.Chandrasekaran,K.Chaudhuri,I.Giacomelli,S.Jha,andS.Yan, [66] S. L. Hyland and S. Tople, “On the intrinsic privacy of stochastic
“Model extraction and active learning,” CoRR, vol. abs/1811.02054, gradient descent,” CoRR, vol. abs/1912.02919, 2019. arXiv: 1912.
2018. arXiv: 1811.02054. [Online]. Available: http://arxiv.org/abs/ 02919.[Online].Available:http://arxiv.org/abs/1912.02919.
1811.02054. [67] J.Yosinski,J.Clune,Y.Bengio,andH.Lipson,“Howtransferableare
[45] T.Lee,B.Edwards,I.Molloy,andD.Su,“Defendingagainstmachine featuresindeepneuralnetworks?”InAdvancesinneuralinformation
learningmodelstealingattacksusingdeceptiveperturbations,”arXiv processingsystems,2014,pp.3320–3328.
preprintarXiv:1806.00054,2018. [68] A. M. Saxe, J. L. McClelland, and S. Ganguli, “Exact solutions to
[46] I.M.Alabdulmohsin,X.Gao,andX.Zhang,“Addingrobustnessto the nonlinear dynamics of learning in deep linear neural networks,”
support vector machines against adversarial reverse engineering,” in arXive-prints,arXiv:1312.6120,arXiv:1312.6120,Dec.2013.arXiv:
Proceedingsofthe23rdACMInternationalConferenceonConference 1312.6120[cs.NE].
onInformationandKnowledgeManagement,2014,pp.231–240. [69] F. J. M. Jr., “The kolmogorov-smirnov test for goodness of fit,”
[47] H. Jia, C. A. Choquette-Choo, and N. Papernot, “Entangled Wa- Journal of the American Statistical Association, vol. 46, no. 253,
termarks as a Defense against Model Extraction,” arXiv e-prints, pp. 68–78, 1951. DOI: 10.1080/01621459.1951.10500769. eprint:

--- 第 16 页 ---
16
https://www.tandfonline.com/doi/pdf/10.1080/01621459.1951. ComputinginPython,”NatureMethods,vol.17,pp.261–272,2020.
10500769.[Online].Available:https://www.tandfonline.com/doi/abs/ DOI:10.1038/s41592-019-0686-2.
10.1080/01621459.1951.10500769. [90] D. Dua and C. Graff, UCI machine learning repository, 2017.
[70] B. Hanin and D. Rolnick, “How to start training: The effect of ini- [Online].Available:http://archive.ics.uci.edu/ml.
tializationandarchitecture,”inAdvancesinNeuralInformationPro-
cessingSystems,S.Bengio,H.Wallach,H.Larochelle,K.Grauman,
N. Cesa-Bianchi, and R. Garnett, Eds., vol. 31, Curran Associates,
APPENDIXA
Inc., 2018, pp. 571–581. [Online]. Available: https://proceedings. MARKOVPROCESSESANDENTROPY
neurips.cc/paper/2018/file/d81f9c1be2e08964bf9f24b15f0e4900-
We include additional definitions as they pertain to our
Paper.pdf.
[71] A. Paszke, S. Gross, F. Massa, A. Lerer, J. Bradbury, G. Chanan, proofs in Section VI.
T. Killeen, Z. Lin, N. Gimelshein, L. Antiga, et al., “Pytorch: An
imperativestyle,high-performancedeeplearninglibrary,”inAdvances Definition 3 (Markov Process). A stochastic process is said
inneuralinformationprocessingsystems,2019,pp.8026–8037. to have the Markov property if its future is independent
[72] M.Abadi,P.Barham,J.Chen,Z.Chen,A.Davis,J.Dean,M.Devin,
of its past, when conditioned on its current state, i.e.,
S. Ghemawat, G. Irving, M. Isard, et al., “Tensorflow: A system
forlarge-scalemachinelearning,”in12th{USENIX}symposiumon Pr(W˜
i+1
|W˜
0
...W˜
i
) = Pr(W˜
i+1
|W˜
i
). A stochastic process
operating systems design and implementation ({OSDI} 16), 2016, with the Markov property is said to be a Markov process.
pp.265–283.
[73] S. Chetlur, C. Woolley, P. Vandermersch, J. Cohen, J. Tran, B. Entropy has many interpretations, but one is the amount of
Catanzaro, and E. Shelhamer, “Cudnn: Efficient primitives for deep
information needed to describe a random variable. We now
learning,”arXivpreprintarXiv:1410.0759,2014.
[74] T.M.CoverandJ.A.Thomas,ElementsofInformationTheory(Wiley provide the formal definition based on [88].
Series in Telecommunications and Signal Processing). USA: Wiley-
Interscience,2006,ISBN:0471241954. Definition 4 (Entropy). [74] For a discrete random variable
[75] M.Jagielski,N.Carlini,D.Berthelot,A.Kurakin,andN.Papernot, X, its entropy is defined as
“High accuracy and high fidelity extraction of neural networks,” in
29th{USENIX}SecuritySymposium({USENIX}Security20),2020. (cid:88)
H(X)=− P(x)log (P(x)). (11)
[76] K. He, X. Zhang, S. Ren, and J. Sun, “Deep Residual Learn- b
ing for Image Recognition,” arXiv e-prints, arXiv:1512.03385, x∈X
arXiv:1512.03385,Dec.2015.arXiv:1512.03385[cs.CV].
Definition 5 (Cross-Entropy). For discrete random variables
[77] A. Krizhevsky, “Learning multiple layers of features from tiny
X and Y the cross-entropy of X given Y is defined as
images,”2009.
[78] (). “Reproducibility — PyTorch 1.7.0 documentation,” [Online].
(cid:88) P(x,y)
Available: https://pytorch.org/docs/stable/notes/randomness.html H(X|Y)=− p(x,y)log
(visitedon12/04/2020). b P(y)
[79] P. Yin, M. Pham, A. Oberman, and S. Osher, “Stochastic backward x∈X,y∈Y
euler:Animplicitgradientdescentalgorithmfork-meansclustering,” =− (cid:88) P(x|Y)log (P(x|Y))
JournalofScientificComputing,vol.77,no.2,pp.1133–1146,2018. b
[80] Y. LeCun, L. Jackel, L. Bottou, A. Brunot, C. Cortes, J. Denker, x∈X,y∈Y
H.Drucker,I.Guyon,U.Muller,E.Sackinger,etal.,“Comparison =H(X,Y)−H(Y) (12)
of learning algorithms for handwritten digit recognition,” in Inter-
national conference on artificial neural networks, Perth, Australia, Definition 6 (Entropy Rate of Stationary Stochastic Process).
vol.60,1995,pp.53–60.
Entropy rate of a stationary stochastic process {W } [74] is
[81] Y. LeCun, “The mnist database of handwritten digits,” http://yann. i
lecun.com/exdb/mnist/,1998. defined by
[82] K. Liu, B. Dolan-Gavitt, and S. Garg, “Fine-pruning: Defending
against backdooring attacks on deep neural networks,” in Interna- H(cid:48)(W)= lim H(W |W ,W ,...,W )
n n-1 n-2 1
tionalSymposiumonResearchinAttacks,Intrusions,andDefenses, n→∞
Springer,2018,pp.273–294. and the limit always exists.
[83] J. Frankle and M. Carbin, “The lottery ticket hypothesis: Finding
sparse,trainableneuralnetworks,”arXivpreprintarXiv:1803.03635, Definition 7 (Entropy Rate of Stationary Markov Process).
2018.
For a stationary Markov process {W } [74], the entropy rate
[84] P.MohasselandY.Zhang,“Secureml:Asystemforscalableprivacy- i
preservingmachinelearning,”in2017IEEESymposiumonSecurity is defined as
andPrivacy(SP),2017,pp.19–38.DOI:10.1109/SP.2017.12.
[85] R.C.Merkle,“Adigitalsignaturebasedonaconventionalencryption H(cid:48)(W)= lim H(W |W ,W ,...,W )
n n-1 n-2 1
function,” in Conference on the theory and application of crypto- n→∞
graphictechniques,Springer,1987,pp.369–378. = lim H(W |W )
n n-1
[86] J.K.Salmon,M.A.Moraes,R.O.Dror,andD.E.Shaw,“Parallel n→∞
randomnumbers:Aseasyas1,2,3,”inProceedingsof2011Inter- = lim H(W |W )
2 1
national Conference for High Performance Computing, Networking, n→∞
StorageandAnalysis,2011,pp.1–12. =H(W |W ) (13)
2 1
[87] K. Claessen and M. H. Pałka, “Splittable pseudorandom number
generators using cryptographic hashing,” ACM SIGPLAN Notices,
vol.48,no.12,pp.47–58,2013. APPENDIXB
[88] C.E.Shannon,“Amathematicaltheoryofcommunication,”TheBell INFEASIBILITYOFDIRECTEDRETRAINING
systemtechnicaljournal,vol.27,no.3,pp.379–423,1948.
[89] P. Virtanen, R. Gommers, T. E. Oliphant, M. Haberland, T. Reddy, For an adversary to ensure that weights W(cid:48) converge to the
t
D. Cournapeau, E. Burovski, P. Peterson, W. Weckesser, J. Bright, prover’s obtained final weights W , an adversary can directly
T
S.J.vanderWalt,M.Brett,J.Wilson,K.J.Millman,N.Mayorov,
A.R.J.Nelson,E.Jones,R.Kern,E.Larson,C.J.Carey,˙I.Polat, minimize the difference in their values. This strategy can be
Y. Feng, E. W. Moore, J. VanderPlas, D. Laxalde, J. Perktold, easily carried out by regularization, a common strategy in
R. Cimrman, I. Henriksen, E. A. Quintero, C. R. Harris, A. M. machine learning to limit the effective capacity of a model.
Archibald, A. H. Ribeiro, F. Pedregosa, P. van Mulbregt, and SciPy
To this end, a practitioner will include an additional term in
1.0Contributors,“SciPy1.0:FundamentalAlgorithmsforScientific
their loss function that minimizes an l norm of the weights.
p

--- 第 17 页 ---
17
An adversary may minimally modify any common regularizer     
to instead minimize d(W(cid:48),W ), as shown in Eq. (14). This
t T     
regularizer cannot pass verification because it requires an
additional state, consisting of the final model weights W ,      T
that does not pass the KT test and which does not have its
    
own valid PoL. Thus, an adversary may attempt to distill the
contained information into a component of the PoL that does     
                 
notrequiretest:theinputsx.However,thisstrategycannotbe  0 H P R U \  & R V W   0 % 
implemented with any gradient-based optimization techniques
asnogradientexists(seeEq.(15)).Anadversarymayattempt
to use gradient-free optimization techniques; our experiments
showthatthisrequiresfarmorefunctioncallsthanthetraining
process itself, due to the highly nonlinear relation between x
and ∇ (L).
f(x)
loss=L(f (x),y)+d(W(cid:48),W ) (14)
wt t T
∇ (d(W(cid:48),W ))=0 (15)
x t T
APPENDIXC
TABLEOFNOTATIONS
Symbol Explanation
PoL Proof-of-learning
T Prover V Verifier
V(.) VerifierV’sVERIFYfunction
f /fT Model/ofproverT
D∈Rn×d d-dimensionaldatasetofnsamples
P /P(f) Proof-of-learning/ofmodelf
W /Wt Modelweights/Modelweightsatstept
M Meta-data
W˜ ={W,M} Modelweightsandlearningmeta-data
L/Lˆ Lossfunction/Averageloss
εrepr(t) reproductionerrorofatrainingstep.
||εrepr|| normalizedreproductionerror.
d ref averagedistancebetween2irrelevantmodels
withthesamearchitectureanddataset
ci therandomvariablethatrepresentsthe
numberoftimesdatapointsiischosenby
VerifyEpochinAlgorithm2
TABLE IV: Notations
APPENDIXD
NOTESFROMSPOOFINGSECTIONS
a) Choosing a Root Solver: We choose three represen-
tative root solvers: Gradient Descent on the l2-norm, Newton
Krylov [89], and Broyden1 [89] to solve Eq. (9), i.e., find its
roots. We use a logistic regression model with 22 neurons on
the Iris dataset [90] and calculated e (see § VI-C2)
reproduce
at each iteration of the root solver. From Figure 8, 9, and 10
we observed that Newton-Krlov performed the best, i.e.,
converged the fastest, and so was the solver we used.
b) Measuring Computational Complexity of Inverting:
Computational complexity necessarily varies with the method
used and implementation of that method, alongside other
miscellaneous overhead. However, a lower bound for the
computational complexity is simply the number of function
callsittakesperstep.Ascomputingβ(w )isdominatedby
T−1
computing a training step (+ any overhead), we have that the
numberoffunctioncallseffectivelymeasureshowmuchmore
)||rper
||(xam
  
||Wt,W0t||1
||Wt,W0t||2
cos(Wt,W0t)   
||Wt,W0t||
 
 
                   
 0 H P R U \  & R V W   0 % 
(a) CIFAR-10
)||rper
||(xam
||Wt,W0t||1
||Wt,W0t||2
cos(Wt,W0t)
||Wt,W0t||
(b) CIFAR-100
Fig. 6: This is the same as Figure 2 except the x-axis is in
megabytes (MB). The memory cost is significantly higher for
CIFAR-100 because we used a much deeper model than the
one used for CIFAR-10.
 9
 H
 U  L I    
 L F  D  
 W L R  
 Q
  7  
 L P
  
 H
   
 
   
    
  
    4   
                       N
            
Fig.7:Analyticalrelationamongverificationtimecost,check-
pointing interval(k), and Q. Note here the verification time
is measured in proportion to the training time (i.e., 100%
means verifing the PoL takes the same time as training the
model). By doing so, verification only depends on k, Q, and
sizeofthetrainingdataset.Therefore,thisfigureisapplicable
tobothCIFAR-10andCIFAR-100(oranydatasetwith50,000
training samples).
computationally intensive inverting is (ex. 5 function calls per
stepwouldbeatleast5timesasintensiveastraining).Thiscan
be converted to flops by simply taking the flops per backward
pass and multiplying that by the number of function calls if
comparisons between architectures are needed (in which case
the ratio is simply the ratio of flops per backward pass).
As seen in Figures 14 and 12, and noting the baseline for
training is simply a y = x line, i.e slope is 1, our current
setup is magnitudes more expensive than training. We leave
improving this for future work.
APPENDIXE
ADDITIONALFIGURESANDTABLES
CIFAR-10 CIFAR-100
d
fer
(cid:96)1 27204.55(±57.384) 189093.15(±558.821)
(cid:96)2 71.431(±0.243) 58.17(±0.142)
(cid:96)∞ 2.678(±0.267) 0.898(±0.135)
cos 0.83(±0.005) 0.847(±0.003)
TABLEV:Referencedistance,d ,ofCIFAR-10andCIFAR-
ref
100.d isdefinedastheaveragedistancebetweenparameters
ref
of two models with the same architecture and dataset, but
trained independently.

--- 第 18 页 ---
18
   
   
   
   
   
            
 6 W H S V
)
(rper
  H  
    
   
   
Fig. 8: Gradient descent has a linear convergence rate when
measuring the l norm.
∞
 
  
    
 
  
     
 6 W H S V
)
(rper
    
   
   
Fig. 9: Newton Krylov completely converges to 0 when
measuring the l norm.
∞
 
  
 
  
 
  
    
 
  
            
 6 W H S V
)
(rper
    
   
   
Fig. 10: Broyden’s method converges to below 1e−7 when
measuring the l norm.
∞
 
  
 
  
 
  
    
       
 6 W H S V
)
(rper
    
    
    
 
                
 6 W H S V
        L W H U   
        L W H U   
       L W H U   
       L W H U   
Fig. 11: Inverting gradients on LeNet5 leads to an l error
inf
that is several orders of magnitude higher as the learning rate
increases from 0.1 to 0.01.
 V O O D &  Q R L W F Q X )  I R  U H E P X 1         L W H U   
        L W H U   
       L W H U   
       L W H U   
 W U D L Q L Q J
Fig. 12: Observe that the function calls grow linearly with the
steps, and that compared to the baseline of training, they are
an order of magnitude steeper.
   
   
   
   
   
       
 6 W H S V
)2(rper
   
   
104 101    
103 100
102 101    
       
 6 W H S V
(a) (cid:96) distance
2
)
(rper
104 101
103 100
102 101
(b) (cid:96) distance
∞
Fig. 13: Observe that for larger learning rates, numerical
methods are unable to converge to a sufficiently small error.
Thus, using large learning rates is infeasible.
    
    
    
 
                
 6 W H S V
 V O O D &  Q R L W F Q X )  I R  U H E P X 1
10 4 10 1
10 3 100
10 2 101
Fig. 14: Observe that the function calls of all learning rates
tested are a magnitude or more larger than the baseline of
training, which would be the line y =x.
   
   
   
   
   
                
 6 W H S V
 H F Q D W V L '  H Q L V R &
10 4 10 1
10 3 100
10 2 101
Fig.15:Observethatthecosinemeasurerelativetothetrained
sequence for all learning rates tested steadily decreases.
