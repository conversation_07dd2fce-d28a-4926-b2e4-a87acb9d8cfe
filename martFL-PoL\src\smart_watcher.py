#!/usr/bin/env python3
"""
智能Watcher - 解决CUDA初始化和fork冲突问题
提供跨平台兼容的进程监控解决方案
"""

import os
import sys
import signal
import platform
import threading
import logging

logger = logging.getLogger(__name__)


class SmartWatcher:
    """
    智能Watcher类 - 跨平台兼容的进程监控
    
    解决原始Watcher的问题：
    1. Windows不支持fork()
    2. CUDA初始化后fork()会导致问题
    3. 提供优雅的退出机制
    """
    
    def __init__(self, enable_fork=None):
        """
        初始化智能Watcher
        
        Args:
            enable_fork: 是否启用fork（None=自动检测）
        """
        self.platform = platform.system()
        self.child_pid = None
        self.enable_fork = self._should_enable_fork() if enable_fork is None else enable_fork
        
        if self.enable_fork:
            self._init_with_fork()
        else:
            self._init_with_signal()
    
    def _should_enable_fork(self):
        """判断是否应该启用fork"""
        # Windows不支持fork
        if self.platform == "Windows":
            return False
        
        # 检查是否已经初始化了CUDA
        try:
            import torch
            if torch.cuda.is_initialized():
                logger.warning("CUDA已初始化，禁用fork以避免冲突")
                return False
        except ImportError:
            pass
        
        # 检查环境变量
        if os.environ.get('DISABLE_FORK', 'false').lower() == 'true':
            return False
        
        return True
    
    def _init_with_fork(self):
        """使用fork方式初始化（类似原始Watcher）"""
        try:
            self.child_pid = os.fork()
            if self.child_pid == 0:
                # 子进程
                return
            else:
                # 父进程
                self._watch_with_fork()
        except OSError as e:
            logger.error(f"Fork失败: {e}，回退到信号处理模式")
            self._init_with_signal()
    
    def _init_with_signal(self):
        """使用信号处理方式初始化"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("智能Watcher已启动（信号处理模式）")
    
    def _watch_with_fork(self):
        """Fork模式的监控循环"""
        try:
            os.wait()
        except KeyboardInterrupt:
            self._kill_child()
        except OSError:
            pass
        finally:
            os._exit(0)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，正在优雅退出...")
        self._cleanup()
        sys.exit(0)
    
    def _kill_child(self):
        """杀死子进程"""
        if self.child_pid:
            try:
                os.kill(self.child_pid, signal.SIGKILL)
            except OSError:
                pass
    
    def _cleanup(self):
        """清理资源"""
        if self.child_pid:
            self._kill_child()
        
        # 清理临时文件等
        try:
            # 这里可以添加其他清理逻辑
            pass
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")


def create_watcher(prefer_smart=True):
    """
    创建合适的Watcher实例
    
    Args:
        prefer_smart: 是否优先使用SmartWatcher
        
    Returns:
        Watcher实例
    """
    if prefer_smart:
        try:
            return SmartWatcher()
        except Exception as e:
            logger.warning(f"SmartWatcher创建失败: {e}，回退到原始Watcher")
    
    # 回退到原始Watcher
    try:
        from watcher import Watcher
        return Watcher()
    except Exception as e:
        logger.error(f"原始Watcher也创建失败: {e}")
        return None


# 兼容性别名
Watcher = SmartWatcher

if __name__ == "__main__":
    # 测试代码
    print("测试SmartWatcher...")
    watcher = SmartWatcher()
    print("SmartWatcher创建成功")
    
    import time
    print("等待5秒后退出...")
    time.sleep(5)
    print("测试完成")
