import argparse

def Config():
    parser = argparse.ArgumentParser()
    parser.add_argument('-m','--model_name', type=str, default='CNN_Model', help='model name')
    parser.add_argument('-ds','--dataset', type=str, default='MNIST', help='dataset')
    parser.add_argument('-ag','--aggregator', type=str, default='FedAvg', help='aggregator')
    parser.add_argument('-np','--n_participant', type=int, default=10, help='the number of clients in model training')
    parser.add_argument('-a','--attack', type=str, default='None', help='attack method')
    parser.add_argument('-na','--n_adversary', type=int, default=0, help='the number of clients is malicious')
    parser.add_argument('-lr','--learning_rate', type=float, default=1e-3, help='the learning rate in model training')
    parser.add_argument('-ld','--lr_decay',type=int,default=0,help='the number of epoches to decay learning rate')
    parser.add_argument('-o','--optimizer', type=str,default='SGD', help='optimizer in model training')
    parser.add_argument('-lf','--loss_fn', type=str,default='CE', help='loss function')
    parser.add_argument('-bz','--batch_size',type=int, default=64, help='the sample number in each batch')
    parser.add_argument('-ge','--global_epoch',type=int, default=10, help='rounds to aggregate')
    parser.add_argument('-le','--local_epoch', type=int, default=2, help='rounds to train locally')
    parser.add_argument('-ss','--sample_split', type=str, default='silo', help='the distribution of sample number')
    parser.add_argument('-cs','--class_split', type=str, default='uni', help='the distribution of class number')
    parser.add_argument('-dv','--device',type=str,default='cuda:0',help='the device used to train model')
    parser.add_argument('-se','--semaphore',type=int,default=10,help='semaphore number')
    parser.add_argument('-al','--alpha',type=int,default=0,help='alpha')
    parser.add_argument('-rd','--root_dataset',type=int,default=100,help='size of root dataset')
    parser.add_argument('-cb','--change_base',type=bool,default=False,help='change base')
    parser.add_argument('-sr','--server_ratio',type=float,default=0.5,help='server train-test ratio')
    parser.add_argument('-q','--quantization',type=bool,default=False,help='quantized aggregation')
    parser.add_argument('-ft','--fine_tuning',type=bool,default=False,help='fine-tuning')
    parser.add_argument('-fte','--fine_tuning_epoch',type=int,default=100,help='fine-tuning epoch')
    
    return parser.parse_args()