#!/usr/bin/env python3
"""
熵增长分析模块 - 基于PoL论文Section VII的安全性理论
实现梯度下降过程中的熵增长分析，用于检测恶意行为
"""

import numpy as np
import torch
import logging
from typing import List, Dict, Any, Optional, Tuple
from scipy import stats
from scipy.stats import kstest, entropy

logger = logging.getLogger(__name__)


class EntropyAnalyzer:
    """
    熵增长分析器 - 基于PoL论文的安全性理论
    
    实现论文Section VII中的熵增长分析：
    1. 梯度下降过程中的熵增长检测
    2. KS测试验证初始化的随机性
    3. 参数分布的统计分析
    4. 异常训练行为检测
    """
    
    def __init__(self, ks_test_threshold: float = 0.01, 
                 entropy_window_size: int = 10,
                 min_entropy_increase: float = 0.01):
        """
        初始化熵增长分析器
        
        Args:
            ks_test_threshold: KS测试显著性水平α（论文建议0.01）
            entropy_window_size: 熵计算的窗口大小
            min_entropy_increase: 最小熵增长阈值
        """
        self.ks_test_threshold = ks_test_threshold
        self.entropy_window_size = entropy_window_size
        self.min_entropy_increase = min_entropy_increase
        
        # 存储历史数据用于分析
        self.weight_history = []
        self.entropy_history = []
        self.gradient_history = []
        
        logger.info(f"熵增长分析器初始化: α={ks_test_threshold}, 窗口={entropy_window_size}")
    
    def analyze_initialization(self, initial_weights: torch.Tensor) -> Dict[str, Any]:
        """
        分析模型初始化的随机性 - 基于PoL论文的KS测试
        
        Args:
            initial_weights: 初始权重张量
            
        Returns:
            分析结果字典
        """
        try:
            weights_flat = initial_weights.flatten().cpu().numpy()
            
            # KS测试检验是否符合正态分布（常见的初始化分布）
            ks_statistic, ks_p_value = kstest(weights_flat, 'norm')
            
            # 检验是否符合均匀分布
            ks_uniform_stat, ks_uniform_p = kstest(weights_flat, 'uniform')
            
            # 计算基本统计量
            mean_val = np.mean(weights_flat)
            std_val = np.std(weights_flat)
            skewness = stats.skew(weights_flat)
            kurtosis = stats.kurtosis(weights_flat)
            
            # 判断初始化是否合理
            is_random_init = (ks_p_value > self.ks_test_threshold or 
                             ks_uniform_p > self.ks_test_threshold)
            
            result = {
                'is_random_initialization': is_random_init,
                'ks_normal_statistic': float(ks_statistic),
                'ks_normal_p_value': float(ks_p_value),
                'ks_uniform_statistic': float(ks_uniform_stat),
                'ks_uniform_p_value': float(ks_uniform_p),
                'mean': float(mean_val),
                'std': float(std_val),
                'skewness': float(skewness),
                'kurtosis': float(kurtosis),
                'analysis': self._interpret_initialization_analysis(
                    is_random_init, ks_p_value, ks_uniform_p, mean_val, std_val
                )
            }
            
            logger.info(f"初始化分析完成: 随机性={is_random_init}, KS_p={ks_p_value:.6f}")
            return result
            
        except Exception as e:
            logger.error(f"初始化分析失败: {e}")
            return {'is_random_initialization': False, 'error': str(e)}
    
    def analyze_training_step(self, current_weights: torch.Tensor, 
                             previous_weights: Optional[torch.Tensor] = None,
                             gradients: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """
        分析单个训练步骤的熵变化
        
        Args:
            current_weights: 当前权重
            previous_weights: 前一步权重
            gradients: 梯度信息
            
        Returns:
            熵分析结果
        """
        try:
            current_flat = current_weights.flatten().cpu().numpy()
            
            # 计算当前权重的熵
            current_entropy = self._calculate_entropy(current_flat)
            self.entropy_history.append(current_entropy)
            self.weight_history.append(current_flat.copy())
            
            result = {
                'current_entropy': float(current_entropy),
                'entropy_trend': None,
                'gradient_entropy': None,
                'anomaly_detected': False
            }
            
            # 如果有前一步权重，计算熵变化
            if previous_weights is not None:
                prev_flat = previous_weights.flatten().cpu().numpy()
                prev_entropy = self._calculate_entropy(prev_flat)
                
                entropy_change = current_entropy - prev_entropy
                result['entropy_change'] = float(entropy_change)
                result['entropy_trend'] = self._analyze_entropy_trend()
                
                # 检测异常的熵变化
                if len(self.entropy_history) >= 3:
                    result['anomaly_detected'] = self._detect_entropy_anomaly()
            
            # 如果有梯度信息，分析梯度熵
            if gradients is not None:
                grad_flat = gradients.flatten().cpu().numpy()
                grad_entropy = self._calculate_entropy(grad_flat)
                result['gradient_entropy'] = float(grad_entropy)
                self.gradient_history.append(grad_entropy)
            
            return result
            
        except Exception as e:
            logger.error(f"训练步骤熵分析失败: {e}")
            return {'current_entropy': 0.0, 'error': str(e)}
    
    def analyze_training_trajectory(self, weight_checkpoints: List[torch.Tensor]) -> Dict[str, Any]:
        """
        分析完整的训练轨迹熵变化
        
        Args:
            weight_checkpoints: 权重检查点列表
            
        Returns:
            轨迹分析结果
        """
        try:
            if len(weight_checkpoints) < 2:
                return {'error': '检查点数量不足'}
            
            entropies = []
            entropy_changes = []
            
            # 计算每个检查点的熵
            for weights in weight_checkpoints:
                flat_weights = weights.flatten().cpu().numpy()
                entropy_val = self._calculate_entropy(flat_weights)
                entropies.append(entropy_val)
            
            # 计算熵变化
            for i in range(1, len(entropies)):
                entropy_changes.append(entropies[i] - entropies[i-1])
            
            # 统计分析
            total_entropy_increase = entropies[-1] - entropies[0]
            avg_entropy_change = np.mean(entropy_changes)
            entropy_variance = np.var(entropy_changes)
            
            # 检测异常模式
            anomalies = self._detect_trajectory_anomalies(entropies, entropy_changes)
            
            # 验证熵增长假设（PoL论文的核心理论）
            entropy_increasing = total_entropy_increase > self.min_entropy_increase
            consistent_increase = np.sum(np.array(entropy_changes) > 0) / len(entropy_changes) > 0.6
            
            result = {
                'total_entropy_increase': float(total_entropy_increase),
                'average_entropy_change': float(avg_entropy_change),
                'entropy_variance': float(entropy_variance),
                'entropy_increasing_trend': entropy_increasing,
                'consistent_increase_ratio': float(np.sum(np.array(entropy_changes) > 0) / len(entropy_changes)),
                'trajectory_valid': entropy_increasing and consistent_increase,
                'anomalies_detected': anomalies,
                'entropy_sequence': [float(e) for e in entropies],
                'entropy_changes': [float(c) for c in entropy_changes]
            }
            
            logger.info(f"轨迹熵分析完成: 总增长={total_entropy_increase:.6f}, 有效={result['trajectory_valid']}")
            return result
            
        except Exception as e:
            logger.error(f"训练轨迹分析失败: {e}")
            return {'error': str(e)}
    
    def _calculate_entropy(self, weights: np.ndarray, bins: int = 50) -> float:
        """
        计算权重分布的熵
        
        使用直方图方法计算连续分布的熵
        """
        try:
            # 创建直方图
            hist, _ = np.histogram(weights, bins=bins, density=True)
            
            # 归一化为概率分布
            hist = hist + 1e-10  # 避免log(0)
            prob_dist = hist / np.sum(hist)
            
            # 计算熵
            entropy_val = entropy(prob_dist, base=2)
            return entropy_val
            
        except Exception as e:
            logger.error(f"熵计算失败: {e}")
            return 0.0
    
    def _analyze_entropy_trend(self) -> str:
        """分析熵变化趋势"""
        if len(self.entropy_history) < 3:
            return "insufficient_data"
        
        recent_entropies = self.entropy_history[-self.entropy_window_size:]
        
        # 计算趋势
        x = np.arange(len(recent_entropies))
        slope, _, r_value, _, _ = stats.linregress(x, recent_entropies)
        
        if slope > 0.001 and r_value > 0.5:
            return "increasing"
        elif slope < -0.001 and r_value > 0.5:
            return "decreasing"
        else:
            return "stable"
    
    def _detect_entropy_anomaly(self) -> bool:
        """检测熵变化异常"""
        if len(self.entropy_history) < 5:
            return False
        
        recent_changes = np.diff(self.entropy_history[-5:])
        
        # 检测突然的熵下降（可能的恶意行为）
        sudden_decrease = np.any(recent_changes < -0.1)
        
        # 检测熵停止增长（可能的懒惰训练）
        no_increase = np.all(recent_changes <= 0)
        
        return sudden_decrease or no_increase
    
    def _detect_trajectory_anomalies(self, entropies: List[float], 
                                   entropy_changes: List[float]) -> List[str]:
        """检测训练轨迹中的异常模式"""
        anomalies = []
        
        # 检测熵突然下降
        large_decreases = [i for i, change in enumerate(entropy_changes) if change < -0.05]
        if large_decreases:
            anomalies.append(f"large_entropy_decrease_at_steps_{large_decreases}")
        
        # 检测熵停滞
        if len(entropy_changes) > 5:
            recent_changes = entropy_changes[-5:]
            if np.all(np.abs(recent_changes) < 0.001):
                anomalies.append("entropy_stagnation")
        
        # 检测异常的熵波动
        if len(entropy_changes) > 3:
            entropy_std = np.std(entropy_changes)
            if entropy_std > 0.1:
                anomalies.append("high_entropy_volatility")
        
        return anomalies
    
    def _interpret_initialization_analysis(self, is_random: bool, ks_normal_p: float, 
                                         ks_uniform_p: float, mean: float, std: float) -> str:
        """解释初始化分析结果"""
        if not is_random:
            return "可能的非随机初始化，需要进一步检查"
        
        if ks_normal_p > 0.05:
            return f"符合正态分布初始化 (p={ks_normal_p:.4f})"
        elif ks_uniform_p > 0.05:
            return f"符合均匀分布初始化 (p={ks_uniform_p:.4f})"
        else:
            return "初始化分布未知，但具有随机性"
    
    def get_security_assessment(self) -> Dict[str, Any]:
        """
        获取基于熵分析的安全性评估
        
        Returns:
            安全性评估结果
        """
        try:
            if len(self.entropy_history) < 2:
                return {'assessment': 'insufficient_data'}
            
            # 计算总体熵增长
            total_increase = self.entropy_history[-1] - self.entropy_history[0]
            
            # 计算一致性
            increases = sum(1 for i in range(1, len(self.entropy_history)) 
                          if self.entropy_history[i] > self.entropy_history[i-1])
            consistency = increases / (len(self.entropy_history) - 1)
            
            # 安全性评级
            if total_increase > 0.1 and consistency > 0.7:
                security_level = "high"
            elif total_increase > 0.05 and consistency > 0.5:
                security_level = "medium"
            else:
                security_level = "low"
            
            return {
                'security_level': security_level,
                'total_entropy_increase': float(total_increase),
                'consistency_ratio': float(consistency),
                'steps_analyzed': len(self.entropy_history),
                'recommendation': self._get_security_recommendation(security_level)
            }
            
        except Exception as e:
            logger.error(f"安全性评估失败: {e}")
            return {'assessment': 'error', 'error': str(e)}
    
    def _get_security_recommendation(self, security_level: str) -> str:
        """获取安全性建议"""
        recommendations = {
            'high': "训练过程显示良好的熵增长模式，安全性较高",
            'medium': "训练过程基本正常，建议增加验证频率",
            'low': "检测到异常的熵变化模式，建议详细检查训练过程"
        }
        return recommendations.get(security_level, "未知安全级别")
