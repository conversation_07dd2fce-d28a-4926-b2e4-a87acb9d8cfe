#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验分析器 - 自动分析实验结果
支持多维度分析，生成可视化报告，便于远程分析
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging
import re
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入依赖，如果失败则跳过
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except (ImportError, ValueError) as e:
    print(f"pandas/numpy导入失败 ({e})，将跳过数据分析功能")
    PANDAS_AVAILABLE = False
    # 创建占位符
    class MockPd:
        @staticmethod
        def DataFrame(*args, **kwargs):
            return type('MockDF', (), {
                'to_json': lambda self, *a, **k: '{}',
                'to_dict': lambda self, *a, **k: {},
                'groupby': lambda self, *a, **k: self,
                'mean': lambda self, *a, **k: self,
                'std': lambda self, *a, **k: self,
                'values': []
            })()

    class MockNp:
        @staticmethod
        def mean(*args, **kwargs): return 0
        @staticmethod
        def std(*args, **kwargs): return 0
        @staticmethod
        def linspace(*args, **kwargs): return [0, 1, 2]
        @staticmethod
        def array(*args, **kwargs): return []

    pd = MockPd()
    np = MockNp()

try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import seaborn as sns
    # 设置字体
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    MATPLOTLIB_AVAILABLE = True
except (ImportError, ValueError) as e:
    print(f"matplotlib/seaborn导入失败 ({e})，将跳过图表生成功能")
    MATPLOTLIB_AVAILABLE = False
    # 创建占位符类避免错误
    class MockPlt:
        @staticmethod
        def figure(*args, **kwargs): pass
        @staticmethod
        def savefig(*args, **kwargs): pass
        @staticmethod
        def close(*args, **kwargs): pass
        @staticmethod
        def tight_layout(*args, **kwargs): pass
        @staticmethod
        def xlabel(*args, **kwargs): pass
        @staticmethod
        def ylabel(*args, **kwargs): pass
        @staticmethod
        def title(*args, **kwargs): pass
        @staticmethod
        def legend(*args, **kwargs): pass
        @staticmethod
        def grid(*args, **kwargs): pass
        @staticmethod
        def plot(*args, **kwargs): pass
        @staticmethod
        def bar(*args, **kwargs): pass
        cm = type('cm', (), {'tab10': lambda x: ['blue', 'orange', 'green', 'red', 'purple']})()

    class MockSns:
        @staticmethod
        def heatmap(*args, **kwargs): pass
        @staticmethod
        def barplot(*args, **kwargs): pass

    plt = MockPlt()
    sns = MockSns()

class ExperimentAnalyzer:
    """实验结果分析器"""
    
    def __init__(self, results_dir: str = "experiment_results"):
        """
        初始化分析器
        
        Args:
            results_dir: 实验结果目录
        """
        self.results_dir = Path(results_dir)
        self.logger = self._setup_logger()
        
        # 分析数据
        self.experiment_data: List[Dict[str, Any]] = []
        self.performance_data = None
        if PANDAS_AVAILABLE:
            import pandas as pd
            self.performance_data = pd.DataFrame()
        self.summary_stats: Dict[str, Any] = {}
        
        # 加载数据
        self._load_experiment_data()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ExperimentAnalyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_experiment_data(self):
        """加载实验数据"""
        if not self.results_dir.exists():
            self.logger.warning(f"结果目录不存在: {self.results_dir}")
            return
        
        experiment_dirs = [d for d in self.results_dir.iterdir() 
                          if d.is_dir() and d.name.startswith('exp_')]
        
        self.logger.info(f"发现 {len(experiment_dirs)} 个实验目录")
        
        for exp_dir in experiment_dirs:
            try:
                exp_data = self._parse_experiment_directory(exp_dir)
                if exp_data:
                    self.experiment_data.append(exp_data)
            except Exception as e:
                self.logger.warning(f"解析实验目录失败 {exp_dir}: {e}")
        
        # 转换为DataFrame
        if self.experiment_data and PANDAS_AVAILABLE:
            import pandas as pd
            self.performance_data = pd.DataFrame(self.experiment_data)
            self.logger.info(f"成功加载 {len(self.experiment_data)} 个实验的数据")
        elif self.experiment_data:
            self.logger.info(f"成功加载 {len(self.experiment_data)} 个实验的数据（无pandas支持）")
        else:
            self.logger.warning("未找到有效的实验数据")
    
    def _parse_experiment_directory(self, exp_dir: Path) -> Optional[Dict[str, Any]]:
        """解析单个实验目录"""
        config_file = exp_dir / "config.json"
        stdout_file = exp_dir / "stdout.log"
        stderr_file = exp_dir / "stderr.log"
        
        if not config_file.exists():
            return None
        
        # 加载配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 解析输出日志
        performance_metrics = {}
        if stdout_file.exists():
            performance_metrics = self._parse_stdout_log(stdout_file)
        
        # 检查错误
        error_info = {}
        if stderr_file.exists():
            error_info = self._parse_stderr_log(stderr_file)
        
        # 合并数据
        exp_data = {
            **config,
            **performance_metrics,
            **error_info,
            'experiment_dir': str(exp_dir),
            'has_error': bool(error_info.get('error_messages'))
        }
        
        return exp_data
    
    def _parse_stdout_log(self, stdout_file: Path) -> Dict[str, Any]:
        """解析标准输出日志"""
        metrics = {}
        
        try:
            with open(stdout_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取准确率信息
            accuracy_pattern = r'Accuracy:\s*([\d.]+)%'
            accuracies = re.findall(accuracy_pattern, content)
            if accuracies:
                try:
                    metrics['final_accuracy'] = float(accuracies[-1])
                    metrics['accuracy_history'] = [float(acc) for acc in accuracies]
                    metrics['max_accuracy'] = max(metrics['accuracy_history'])
                    metrics['min_accuracy'] = min(metrics['accuracy_history'])
                except ValueError as e:
                    self.logger.warning(f"解析准确率失败: {e}, 数据: {accuracies}")
            
            # 提取损失信息
            loss_pattern = r'Loss:\s*([\d.]+)\.'
            losses = re.findall(loss_pattern, content)
            if losses:
                try:
                    metrics['final_loss'] = float(losses[-1])
                    metrics['loss_history'] = [float(loss) for loss in losses]
                    metrics['min_loss'] = min(metrics['loss_history'])
                except ValueError as e:
                    self.logger.warning(f"解析损失失败: {e}, 数据: {losses}")
            
            # 提取攻击成功率
            attack_acc_pattern = r'Attack Accuracy:\s*([\d.]+)'
            attack_accs = re.findall(attack_acc_pattern, content)
            if attack_accs:
                metrics['final_attack_accuracy'] = float(attack_accs[-1])
                metrics['attack_accuracy_history'] = [float(acc) for acc in attack_accs]
            
            # 提取F1分数
            f1_pattern = r'F1:\s*([\d.]+)'
            f1_scores = re.findall(f1_pattern, content)
            if f1_scores:
                metrics['final_f1'] = float(f1_scores[-1])
                metrics['f1_history'] = [float(f1) for f1 in f1_scores]
            
            # 提取Kappa系数
            kappa_pattern = r'Kappa:\s*([\d.]+)'
            kappa_scores = re.findall(kappa_pattern, content)
            if kappa_scores:
                metrics['final_kappa'] = float(kappa_scores[-1])
                metrics['kappa_history'] = [float(kappa) for kappa in kappa_scores]
            
            # 提取PoL验证信息
            pol_pattern = r'PoL验证通过率:\s*([\d.]+)%'
            pol_rates = re.findall(pol_pattern, content)
            if pol_rates:
                metrics['pol_verification_rate'] = float(pol_rates[-1])
            
            # 提取训练时间
            time_pattern = r'训练时间:\s*([\d.]+)s'
            times = re.findall(time_pattern, content)
            if times:
                metrics['training_time'] = float(times[-1])
            
            # 提取收敛轮数
            epoch_pattern = r'Epoch:\s*(\d+)'
            epochs = re.findall(epoch_pattern, content)
            if epochs:
                metrics['total_epochs'] = len(set(epochs))
                metrics['converged_epoch'] = int(epochs[-1]) if epochs else 0
            
        except Exception as e:
            self.logger.warning(f"解析stdout失败: {e}")
        
        return metrics
    
    def _parse_stderr_log(self, stderr_file: Path) -> Dict[str, Any]:
        """解析错误日志"""
        error_info = {}
        
        try:
            with open(stderr_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if content.strip():
                # 提取错误消息
                error_lines = [line.strip() for line in content.split('\n') 
                              if line.strip() and not line.startswith('WARNING')]
                
                error_info['error_messages'] = error_lines
                error_info['error_count'] = len(error_lines)
                
                # 分类错误类型
                if any('CUDA' in line for line in error_lines):
                    error_info['error_type'] = 'CUDA_ERROR'
                elif any('Memory' in line for line in error_lines):
                    error_info['error_type'] = 'MEMORY_ERROR'
                elif any('Timeout' in line for line in error_lines):
                    error_info['error_type'] = 'TIMEOUT_ERROR'
                else:
                    error_info['error_type'] = 'OTHER_ERROR'
            
        except Exception as e:
            self.logger.warning(f"解析stderr失败: {e}")
        
        return error_info
    
    def generate_performance_comparison(self) -> Dict[str, Any]:
        """生成性能对比分析"""
        if not PANDAS_AVAILABLE or self.performance_data is None or self.performance_data.empty:
            return self._generate_basic_performance_comparison()
        
        analysis = {}
        
        # 按聚合器分组分析
        if 'aggregator' in self.performance_data.columns:
            agg_columns = {
                'final_accuracy': ['mean', 'std', 'count'],
                'final_loss': ['mean', 'std']
            }
            # 只有当training_time列存在时才添加
            if 'training_time' in self.performance_data.columns:
                agg_columns['training_time'] = ['mean', 'std']

            agg_stats = self.performance_data.groupby('aggregator').agg(agg_columns).round(4)
            # 转换为可序列化的格式
            agg_dict = {}
            for agg, row in agg_stats.iterrows():
                # 处理多级列名
                row_dict = {}
                for col_tuple, value in row.items():
                    if isinstance(col_tuple, tuple):
                        key = f"{col_tuple[0]}_{col_tuple[1]}"
                    else:
                        key = str(col_tuple)
                    row_dict[key] = value
                agg_dict[agg] = row_dict
            analysis['aggregator_comparison'] = agg_dict
        
        # PoL效果分析
        if 'enable_pol' in self.performance_data.columns:
            pol_comparison = self.performance_data.groupby(['aggregator', 'enable_pol']).agg({
                'final_accuracy': ['mean', 'std'],
                'final_loss': ['mean', 'std']
            }).round(4)
            # 转换为可序列化的格式
            pol_dict = {}
            for (agg, pol), row in pol_comparison.iterrows():
                key = f"{agg}_pol_{pol}"
                # 处理多级列名
                row_dict = {}
                for col_tuple, value in row.items():
                    if isinstance(col_tuple, tuple):
                        col_key = f"{col_tuple[0]}_{col_tuple[1]}"
                    else:
                        col_key = str(col_tuple)
                    row_dict[col_key] = value
                pol_dict[key] = row_dict
            analysis['pol_effect'] = pol_dict
        
        # 攻击鲁棒性分析
        if 'attack' in self.performance_data.columns:
            attack_columns = {'final_accuracy': ['mean', 'std']}
            # 只有当final_attack_accuracy列存在时才添加
            if 'final_attack_accuracy' in self.performance_data.columns:
                attack_columns['final_attack_accuracy'] = ['mean', 'std']

            attack_stats = self.performance_data.groupby(['attack', 'aggregator']).agg(attack_columns).round(4)
            # 转换为可序列化的格式
            attack_dict = {}
            for (attack, agg), row in attack_stats.iterrows():
                key = f"{attack}_{agg}"
                # 处理多级列名
                row_dict = {}
                for col_tuple, value in row.items():
                    if isinstance(col_tuple, tuple):
                        col_key = f"{col_tuple[0]}_{col_tuple[1]}"
                    else:
                        col_key = str(col_tuple)
                    row_dict[col_key] = value
                attack_dict[key] = row_dict
            analysis['attack_robustness'] = attack_dict
        
        # 数据集泛化性分析
        if 'dataset' in self.performance_data.columns:
            dataset_stats = self.performance_data.groupby(['dataset', 'aggregator']).agg({
                'final_accuracy': ['mean', 'std'],
                'final_loss': ['mean', 'std']
            }).round(4)
            # 转换为可序列化的格式
            dataset_dict = {}
            for (dataset, agg), row in dataset_stats.iterrows():
                key = f"{dataset}_{agg}"
                # 处理多级列名
                row_dict = {}
                for col_tuple, value in row.items():
                    if isinstance(col_tuple, tuple):
                        col_key = f"{col_tuple[0]}_{col_tuple[1]}"
                    else:
                        col_key = str(col_tuple)
                    row_dict[col_key] = value
                dataset_dict[key] = row_dict
            analysis['dataset_generalization'] = dataset_dict
        
        return analysis

    def _safe_describe_to_dict(self) -> Dict[str, Any]:
        """安全地将describe结果转换为可序列化的字典"""
        if not PANDAS_AVAILABLE or self.performance_data is None or self.performance_data.empty:
            return {}

        try:
            describe_result = self.performance_data.describe()
            # 转换为可序列化的格式
            result = {}
            for col in describe_result.columns:
                result[col] = describe_result[col].to_dict()
            return result
        except Exception as e:
            self.logger.warning(f"生成数据摘要失败: {e}")
            return {}

    def _generate_basic_performance_comparison(self) -> Dict[str, Any]:
        """生成基础性能对比分析（不依赖pandas）"""
        if not self.experiment_data:
            return {}

        analysis = {}

        # 按聚合器分组
        aggregator_groups = {}
        for exp in self.experiment_data:
            agg = exp.get('aggregator', 'unknown')
            if agg not in aggregator_groups:
                aggregator_groups[agg] = []
            aggregator_groups[agg].append(exp)

        # 计算统计信息
        agg_stats = {}
        for agg, experiments in aggregator_groups.items():
            accuracies = [exp.get('final_accuracy', 0) for exp in experiments if 'final_accuracy' in exp]
            losses = [exp.get('final_loss', 0) for exp in experiments if 'final_loss' in exp]

            if accuracies:
                agg_stats[agg] = {
                    'accuracy_mean': sum(accuracies) / len(accuracies),
                    'accuracy_count': len(accuracies),
                    'loss_mean': sum(losses) / len(losses) if losses else 0,
                    'experiments': len(experiments)
                }

        analysis['aggregator_comparison'] = agg_stats

        # PoL效果分析
        pol_groups = {'with_pol': [], 'without_pol': []}
        for exp in self.experiment_data:
            if exp.get('enable_pol', False):
                pol_groups['with_pol'].append(exp)
            else:
                pol_groups['without_pol'].append(exp)

        pol_stats = {}
        for group, experiments in pol_groups.items():
            accuracies = [exp.get('final_accuracy', 0) for exp in experiments if 'final_accuracy' in exp]
            if accuracies:
                pol_stats[group] = {
                    'accuracy_mean': sum(accuracies) / len(accuracies),
                    'experiments': len(experiments)
                }

        analysis['pol_effect'] = pol_stats

        return analysis

    def generate_visualizations(self, output_dir: Optional[str] = None):
        """生成可视化图表"""
        if not MATPLOTLIB_AVAILABLE:
            self.logger.warning("matplotlib不可用，跳过可视化生成")
            return

        if not PANDAS_AVAILABLE or self.performance_data is None or self.performance_data.empty:
            self.logger.warning("没有数据可供可视化")
            return
        
        if output_dir is None:
            output_dir = self.results_dir / "analysis"
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(exist_ok=True)
        
        # 1. 聚合器性能对比
        self._plot_aggregator_comparison(output_dir)
        
        # 2. PoL效果对比
        self._plot_pol_effect(output_dir)
        
        # 3. 攻击鲁棒性分析
        self._plot_attack_robustness(output_dir)
        
        # 4. 收敛曲线
        self._plot_convergence_curves(output_dir)
        
        # 5. 数据集泛化性
        self._plot_dataset_generalization(output_dir)
        
        self.logger.info(f"可视化图表已保存到: {output_dir}")
    
    def _plot_aggregator_comparison(self, output_dir: Path):
        """绘制聚合器性能对比图"""
        if 'aggregator' not in self.performance_data.columns or 'final_accuracy' not in self.performance_data.columns:
            return

        # 过滤掉没有准确率数据的实验
        valid_data = self.performance_data.dropna(subset=['final_accuracy'])
        if valid_data.empty:
            self.logger.warning("没有有效的准确率数据，跳过聚合器对比图")
            return

        plt.figure(figsize=(12, 8))

        # 准确率对比
        plt.subplot(2, 2, 1)
        try:
            sns.boxplot(data=valid_data, x='aggregator', y='final_accuracy')
            plt.title('聚合器准确率对比')
            plt.xticks(rotation=45)
        except Exception as e:
            self.logger.warning(f"绘制准确率对比图失败: {e}")
            plt.text(0.5, 0.5, f'数据不足\n({len(valid_data)} 个有效实验)',
                    ha='center', va='center', transform=plt.gca().transAxes)
        
        # 损失对比
        if 'final_loss' in self.performance_data.columns:
            plt.subplot(2, 2, 2)
            loss_data = self.performance_data.dropna(subset=['final_loss'])
            if not loss_data.empty:
                try:
                    sns.boxplot(data=loss_data, x='aggregator', y='final_loss')
                    plt.title('聚合器损失对比')
                    plt.xticks(rotation=45)
                except Exception as e:
                    self.logger.warning(f"绘制损失对比图失败: {e}")
                    plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)

        # 训练时间对比
        if 'training_time' in self.performance_data.columns:
            plt.subplot(2, 2, 3)
            time_data = self.performance_data.dropna(subset=['training_time'])
            if not time_data.empty:
                try:
                    sns.boxplot(data=time_data, x='aggregator', y='training_time')
                    plt.title('训练时间对比')
                    plt.xticks(rotation=45)
                except Exception as e:
                    self.logger.warning(f"绘制训练时间对比图失败: {e}")
                    plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)

        # F1分数对比
        if 'final_f1' in self.performance_data.columns:
            plt.subplot(2, 2, 4)
            f1_data = self.performance_data.dropna(subset=['final_f1'])
            if not f1_data.empty:
                try:
                    sns.boxplot(data=f1_data, x='aggregator', y='final_f1')
                except Exception as e:
                    self.logger.warning(f"绘制F1对比图失败: {e}")
                    plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)
            plt.title('F1分数对比')
            plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_dir / "aggregator_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_pol_effect(self, output_dir: Path):
        """绘制PoL效果对比图"""
        if 'enable_pol' not in self.performance_data.columns:
            return

        # 过滤martFL数据
        martfl_data = self.performance_data[self.performance_data['aggregator'] == 'martFL']
        if martfl_data.empty:
            return

        # 检查是否有足够的数据进行对比
        pol_groups = martfl_data['enable_pol'].value_counts()
        if len(pol_groups) < 2:
            self.logger.warning("PoL数据不足，跳过PoL效果分析图")
            return

        plt.figure(figsize=(15, 10))

        # 准确率对比
        plt.subplot(2, 3, 1)
        try:
            # 检查每个组是否有足够的数据
            pol_data_counts = martfl_data.groupby('enable_pol')['final_accuracy'].count()
            if all(count >= 1 for count in pol_data_counts):
                sns.boxplot(data=martfl_data, x='enable_pol', y='final_accuracy')
                plt.title('PoL对准确率的影响')
                plt.xlabel('启用PoL')
            else:
                plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)
                plt.title('PoL对准确率的影响')
        except Exception as e:
            self.logger.warning(f"绘制PoL准确率对比失败: {e}")
            plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)
            plt.title('PoL对准确率的影响')
        
        # 按数据集分组的PoL效果
        if 'dataset' in martfl_data.columns:
            plt.subplot(2, 3, 2)
            try:
                # 检查是否有多个数据集
                unique_datasets = martfl_data['dataset'].nunique()
                unique_pol_values = martfl_data['enable_pol'].nunique()

                if unique_datasets > 1 and unique_pol_values > 1:
                    # 进一步检查每个组合是否有数据
                    dataset_pol_groups = martfl_data.groupby(['dataset', 'enable_pol']).size()
                    if len(dataset_pol_groups) >= 2 and all(count > 0 for count in dataset_pol_groups):
                        sns.boxplot(data=martfl_data, x='dataset', y='final_accuracy', hue='enable_pol')
                        plt.title('不同数据集上的PoL效果')
                        plt.xticks(rotation=45)
                    else:
                        plt.text(0.5, 0.5, '数据分组不足', ha='center', va='center', transform=plt.gca().transAxes)
                        plt.title('不同数据集上的PoL效果')
                else:
                    plt.text(0.5, 0.5, '数据不足\n(需要多个数据集)', ha='center', va='center', transform=plt.gca().transAxes)
                    plt.title('不同数据集上的PoL效果')
            except Exception as e:
                self.logger.warning(f"绘制数据集PoL效果失败: {e}")
                plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)
                plt.title('不同数据集上的PoL效果')

        # 按攻击类型分组的PoL效果
        if 'attack' in martfl_data.columns:
            plt.subplot(2, 3, 3)
            attack_data = martfl_data[martfl_data['attack'] != 'None']
            if not attack_data.empty:
                try:
                    sns.boxplot(data=attack_data, x='attack', y='final_accuracy', hue='enable_pol')
                    plt.title('攻击场景下的PoL效果')
                    plt.xticks(rotation=45)
                except Exception as e:
                    self.logger.warning(f"绘制攻击PoL效果失败: {e}")
                    plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)
        
        # PoL验证通过率
        if 'pol_verification_rate' in martfl_data.columns:
            pol_data = martfl_data[martfl_data['enable_pol'] == True]
            if not pol_data.empty:
                plt.subplot(2, 3, 4)
                sns.histplot(data=pol_data, x='pol_verification_rate', bins=20)
                plt.title('PoL验证通过率分布')
        
        # 训练时间对比
        if 'training_time' in martfl_data.columns:
            plt.subplot(2, 3, 5)
            try:
                sns.boxplot(data=martfl_data, x='enable_pol', y='training_time')
                plt.title('PoL对训练时间的影响')
            except Exception as e:
                self.logger.warning(f"绘制PoL训练时间影响失败: {e}")
                plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)
        
        # 参与者数量对PoL效果的影响
        if 'n_participant' in martfl_data.columns:
            plt.subplot(2, 3, 6)
            sns.scatterplot(data=martfl_data, x='n_participant', y='final_accuracy', hue='enable_pol')
            plt.title('参与者数量对PoL效果的影响')
        
        plt.tight_layout()
        plt.savefig(output_dir / "pol_effect_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_attack_robustness(self, output_dir: Path):
        """绘制攻击鲁棒性分析图"""
        if 'attack' not in self.performance_data.columns:
            return
        
        attack_data = self.performance_data[self.performance_data['attack'] != 'None']
        if attack_data.empty:
            return
        
        plt.figure(figsize=(15, 10))
        
        # 不同攻击下的准确率
        plt.subplot(2, 3, 1)
        sns.boxplot(data=attack_data, x='attack', y='final_accuracy', hue='aggregator')
        plt.title('不同攻击下的模型准确率')
        plt.xticks(rotation=45)
        
        # 攻击成功率
        if 'final_attack_accuracy' in attack_data.columns:
            plt.subplot(2, 3, 2)
            sns.boxplot(data=attack_data, x='attack', y='final_attack_accuracy', hue='aggregator')
            plt.title('攻击成功率')
            plt.xticks(rotation=45)
        
        # PoL在攻击场景下的效果
        if 'enable_pol' in attack_data.columns:
            martfl_attack = attack_data[attack_data['aggregator'] == 'martFL']
            if not martfl_attack.empty:
                plt.subplot(2, 3, 3)
                sns.boxplot(data=martfl_attack, x='attack', y='final_accuracy', hue='enable_pol')
                plt.title('PoL在攻击场景下的防御效果')
                plt.xticks(rotation=45)
        
        # 恶意参与者比例的影响
        if 'n_adversary' in attack_data.columns and 'n_participant' in attack_data.columns:
            attack_data_copy = attack_data.copy()
            attack_data_copy['adversary_ratio'] = attack_data_copy['n_adversary'] / attack_data_copy['n_participant']
            
            plt.subplot(2, 3, 4)
            sns.scatterplot(data=attack_data_copy, x='adversary_ratio', y='final_accuracy', hue='aggregator')
            plt.title('恶意参与者比例对准确率的影响')
        
        # 不同数据集上的攻击效果
        if 'dataset' in attack_data.columns:
            plt.subplot(2, 3, 5)
            sns.boxplot(data=attack_data, x='dataset', y='final_accuracy', hue='attack')
            plt.title('不同数据集上的攻击效果')
            plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_dir / "attack_robustness_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_convergence_curves(self, output_dir: Path):
        """绘制收敛曲线"""
        try:
            # 提取有损失历史的实验
            experiments_with_history = [
                exp for exp in self.experiment_data
                if 'loss_history' in exp and exp['loss_history']
            ]

            if not experiments_with_history:
                self.logger.warning("没有找到损失历史数据，跳过收敛曲线绘制")
                return

            plt.figure(figsize=(12, 8))

            # 按聚合器分组绘制
            aggregators = set(exp.get('aggregator', 'Unknown') for exp in experiments_with_history)
            colors = plt.cm.tab10(np.linspace(0, 1, len(aggregators)))

            for i, aggregator in enumerate(aggregators):
                agg_experiments = [exp for exp in experiments_with_history
                                 if exp.get('aggregator') == aggregator]

                # 计算平均收敛曲线
                max_epochs = max(len(exp['loss_history']) for exp in agg_experiments)
                avg_losses = []

                for epoch in range(max_epochs):
                    epoch_losses = [exp['loss_history'][epoch] for exp in agg_experiments
                                  if epoch < len(exp['loss_history'])]
                    if epoch_losses:
                        avg_losses.append(np.mean(epoch_losses))

                if avg_losses:
                    plt.plot(range(len(avg_losses)), avg_losses,
                           label=f'{aggregator} (n={len(agg_experiments)})',
                           color=colors[i], linewidth=2)

            plt.xlabel('训练轮次')
            plt.ylabel('损失值')
            plt.title('不同聚合方法的收敛曲线对比')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            plt.savefig(output_dir / "convergence_curves.png", dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info(f"收敛曲线已保存到: {output_dir / 'convergence_curves.png'}")

        except Exception as e:
            self.logger.error(f"绘制收敛曲线失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _plot_dataset_generalization(self, output_dir: Path):
        """绘制数据集泛化性分析"""
        if 'dataset' not in self.performance_data.columns:
            return

        # 检查是否有多个数据集
        dataset_counts = self.performance_data['dataset'].value_counts()
        if len(dataset_counts) < 2:
            self.logger.warning("只有一个数据集，跳过数据集泛化性分析图")
            return

        plt.figure(figsize=(12, 8))

        # 不同数据集上的性能
        plt.subplot(2, 2, 1)
        try:
            sns.boxplot(data=self.performance_data, x='dataset', y='final_accuracy', hue='aggregator')
            plt.title('不同数据集上的准确率')
            plt.xticks(rotation=45)
        except Exception as e:
            self.logger.warning(f"绘制数据集准确率对比失败: {e}")
            plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)
        
        # 数据集复杂度 vs 性能
        if 'final_loss' in self.performance_data.columns:
            plt.subplot(2, 2, 2)
            sns.scatterplot(data=self.performance_data, x='final_loss', y='final_accuracy', hue='dataset')
            plt.title('损失 vs 准确率')
        
        # 参与者数量在不同数据集上的影响
        if 'n_participant' in self.performance_data.columns:
            plt.subplot(2, 2, 3)
            sns.scatterplot(data=self.performance_data, x='n_participant', y='final_accuracy', hue='dataset')
            plt.title('参与者数量对不同数据集的影响')
        
        # 训练时间对比
        if 'training_time' in self.performance_data.columns:
            plt.subplot(2, 2, 4)
            try:
                sns.boxplot(data=self.performance_data, x='dataset', y='training_time', hue='aggregator')
                plt.title('不同数据集的训练时间')
                plt.xticks(rotation=45)
            except Exception as e:
                self.logger.warning(f"绘制训练时间对比失败: {e}")
                plt.text(0.5, 0.5, '数据不足', ha='center', va='center', transform=plt.gca().transAxes)
        
        plt.tight_layout()
        plt.savefig(output_dir / "dataset_generalization.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_comprehensive_report(self, output_file: Optional[str] = None) -> Dict[str, Any]:
        """生成综合分析报告"""
        if output_file is None:
            output_file = self.results_dir / "comprehensive_analysis_report.json"
        
        # 生成各种分析
        performance_analysis = self.generate_performance_comparison()
        
        # 基础统计
        basic_stats = {}
        if self.experiment_data:
            datasets = set(exp.get('dataset', 'unknown') for exp in self.experiment_data)
            aggregators = set(exp.get('aggregator', 'unknown') for exp in self.experiment_data)
            pol_experiments = [exp for exp in self.experiment_data if exp.get('enable_pol', False)]
            attack_experiments = [exp for exp in self.experiment_data if exp.get('attack', 'None') != 'None']
            successful_experiments = [exp for exp in self.experiment_data if not exp.get('has_error', False)]

            basic_stats = {
                'total_experiments': len(self.experiment_data),
                'successful_experiments': len(successful_experiments),
                'unique_datasets': len(datasets),
                'unique_aggregators': len(aggregators),
                'pol_experiments': len(pol_experiments),
                'attack_experiments': len(attack_experiments)
            }
        
        # 综合报告
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'basic_statistics': basic_stats,
            'performance_analysis': performance_analysis,
            'data_summary': self._safe_describe_to_dict(),
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"综合分析报告已保存到: {output_file}")
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if self.performance_data.empty:
            recommendations.append("没有找到有效的实验数据，请检查实验执行情况")
            return recommendations
        
        # 基于数据生成建议
        # PoL效果分析
        pol_experiments = [exp for exp in self.experiment_data if exp.get('enable_pol', False)]
        no_pol_experiments = [exp for exp in self.experiment_data if not exp.get('enable_pol', False)]

        if pol_experiments and no_pol_experiments:
            pol_accuracies = [exp.get('final_accuracy', 0) for exp in pol_experiments if 'final_accuracy' in exp]
            no_pol_accuracies = [exp.get('final_accuracy', 0) for exp in no_pol_experiments if 'final_accuracy' in exp]

            if pol_accuracies and no_pol_accuracies:
                pol_acc = sum(pol_accuracies) / len(pol_accuracies)
                no_pol_acc = sum(no_pol_accuracies) / len(no_pol_accuracies)

                if pol_acc > no_pol_acc:
                    recommendations.append(f"PoL显著提升了性能（平均准确率提升 {(pol_acc - no_pol_acc)*100:.2f}%），建议在生产环境中使用")
                else:
                    recommendations.append("PoL对性能提升有限，需要进一步优化参数或算法")
        
        # 检查错误率
        error_experiments = [exp for exp in self.experiment_data if exp.get('has_error', False)]
        if self.experiment_data:
            error_rate = len(error_experiments) / len(self.experiment_data)
            if error_rate > 0.1:
                recommendations.append(f"实验错误率较高（{error_rate*100:.1f}%），建议检查实验环境和参数设置")

        # 性能建议
        accuracies = [exp.get('final_accuracy', 0) for exp in self.experiment_data if 'final_accuracy' in exp]
        if accuracies:
            avg_acc = sum(accuracies) / len(accuracies)
            if avg_acc < 0.8:
                recommendations.append("整体准确率偏低，建议调整超参数或增加训练轮数")
        
        return recommendations

if __name__ == "__main__":
    # 测试分析器
    analyzer = ExperimentAnalyzer("experiment_results")

    # 生成可视化
    analyzer.generate_visualizations()

    # 生成综合报告
    report = analyzer.generate_comprehensive_report()

    print("分析完成！")
